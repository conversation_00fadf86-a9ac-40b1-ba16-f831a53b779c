"""
Phiên bản đơn giản hơn của chiến lược MACD + RSI
"""

import pandas as pd
from typing import Dict, Optional
from datetime import datetime
from strategy import MacdRsiStrategy, Trade
from indicators import MultiTimeframeAnalysis
import config

class SimpleMacdRsiStrategy(MacdRsiStrategy):
    """Chiến lược MACD + RSI đơn giản hơn"""
    
    def check_entry_signals(self, data: Dict[str, pd.DataFrame]) -> Optional[str]:
        """
        Kiểm tra tín hiệu vào lệnh với điều kiện đơn giản hơn
        """
        if self.open_trade is not None:
            return None  # Đã có lệnh mở
        
        # Kiểm tra dữ liệu
        if 'ITF' not in data or data['ITF'].empty:
            return None
        if 'LTF' not in data or data['LTF'].empty:
            return None
        
        itf_latest = data['ITF'].iloc[-1]
        ltf_latest = data['LTF'].iloc[-1]
        
        # Điều kiện LONG đơn giản
        long_conditions = [
            # ITF: RSI oversold và MACD bullish
            itf_latest.get('rsi', 50) < 40,  # RSI dưới 40
            itf_latest.get('macd', 0) > itf_latest.get('macd_signal', 0),  # MACD > Signal
            
            # LTF: Xác nhận momentum
            ltf_latest.get('macd', 0) > ltf_latest.get('macd_signal', 0),  # MACD > Signal
        ]
        
        # Điều kiện SHORT đơn giản
        short_conditions = [
            # ITF: RSI overbought và MACD bearish
            itf_latest.get('rsi', 50) > 60,  # RSI trên 60
            itf_latest.get('macd', 0) < itf_latest.get('macd_signal', 0),  # MACD < Signal
            
            # LTF: Xác nhận momentum
            ltf_latest.get('macd', 0) < ltf_latest.get('macd_signal', 0),  # MACD < Signal
        ]
        
        # Kiểm tra HTF trend (optional)
        htf_trend = 'neutral'
        if 'HTF' in data and not data['HTF'].empty:
            htf_latest = data['HTF'].iloc[-1]
            if htf_latest.get('macd', 0) > htf_latest.get('macd_signal', 0):
                htf_trend = 'bullish'
            elif htf_latest.get('macd', 0) < htf_latest.get('macd_signal', 0):
                htf_trend = 'bearish'
        
        # Quyết định signal
        if all(long_conditions):
            # Chỉ long khi HTF không bearish
            if htf_trend != 'bearish':
                return 'long'
        
        elif all(short_conditions):
            # Chỉ short khi HTF không bullish
            if htf_trend != 'bullish':
                return 'short'
        
        return None
    
    def check_exit_signals(self, data: Dict[str, pd.DataFrame]) -> bool:
        """Kiểm tra tín hiệu thoát lệnh đơn giản"""
        if self.open_trade is None:
            return False
        
        if 'ITF' not in data or data['ITF'].empty:
            return False
        
        itf_latest = data['ITF'].iloc[-1]
        
        if self.open_trade.side == 'long':
            # Exit long khi RSI overbought hoặc MACD bearish
            exit_signal = (
                itf_latest.get('rsi', 50) > 70 or  # RSI overbought
                itf_latest.get('macd', 0) < itf_latest.get('macd_signal', 0)  # MACD bearish
            )
        else:  # short
            # Exit short khi RSI oversold hoặc MACD bullish
            exit_signal = (
                itf_latest.get('rsi', 50) < 30 or  # RSI oversold
                itf_latest.get('macd', 0) > itf_latest.get('macd_signal', 0)  # MACD bullish
            )
        
        return exit_signal

def test_simple_strategy():
    """Test chiến lược đơn giản"""
    from backtest import BacktestEngine
    from data_fetcher import DataFetcher
    from indicators import TechnicalIndicators
    
    print("🧪 TEST SIMPLE STRATEGY")
    print("=" * 50)
    
    # Tạo engine với strategy đơn giản
    engine = BacktestEngine(initial_capital=5000)
    engine.strategy = SimpleMacdRsiStrategy(5000)
    
    # Chuẩn bị dữ liệu
    data = engine.prepare_data(config.SYMBOL, 60)
    if not data or 'LTF' not in data:
        print("❌ Không thể chuẩn bị dữ liệu")
        return
    
    ltf_data = data['LTF']
    total_bars = len(ltf_data)
    start_idx = 50  # Ít hơn để có nhiều dữ liệu test hơn
    
    print(f"📈 Test từ nến {start_idx} đến {total_bars}")
    
    signal_count = 0
    
    # Test signals
    for i in range(start_idx, min(start_idx + 100, total_bars)):  # Test 100 nến
        current_data = {
            'HTF': data['HTF'].iloc[:i+1] if 'HTF' in data else pd.DataFrame(),
            'ITF': data['ITF'].iloc[:i+1] if 'ITF' in data else pd.DataFrame(), 
            'LTF': data['LTF'].iloc[:i+1]
        }
        
        # Kiểm tra signal
        signal = engine.strategy.check_entry_signals(current_data)
        if signal:
            signal_count += 1
            timestamp = ltf_data.index[i]
            price = ltf_data.iloc[i]['close']
            print(f"🎯 Signal {signal_count}: {signal.upper()} tại {timestamp} | Giá: ${price:,.2f}")
    
    print(f"\n📊 Tổng signals tìm thấy: {signal_count}")
    
    if signal_count > 0:
        print("✅ Strategy có thể tạo signals!")
        
        # Chạy backtest thực
        print("\n🚀 Chạy backtest với simple strategy...")
        
        for i in range(start_idx, total_bars):
            current_time = ltf_data.index[i]
            current_data = {
                'HTF': data['HTF'].iloc[:i+1] if 'HTF' in data else pd.DataFrame(),
                'ITF': data['ITF'].iloc[:i+1] if 'ITF' in data else pd.DataFrame(), 
                'LTF': data['LTF'].iloc[:i+1]
            }
            
            current_bar = ltf_data.iloc[i]
            
            # Kiểm tra stop loss / take profit
            engine.strategy.check_stop_loss_take_profit(
                current_time, 
                current_bar['high'], 
                current_bar['low']
            )
            
            # Kiểm tra exit
            if engine.strategy.open_trade is not None:
                if engine.strategy.check_exit_signals(current_data):
                    engine.strategy.close_position(
                        current_time, 
                        current_bar['close'], 
                        'exit_signal'
                    )
            
            # Kiểm tra entry
            if engine.strategy.open_trade is None:
                entry_signal = engine.strategy.check_entry_signals(current_data)
                if entry_signal:
                    engine.strategy.open_position(
                        current_time,
                        current_bar['close'],
                        entry_signal,
                        current_data['LTF']
                    )
        
        # Đóng lệnh cuối nếu còn mở
        if engine.strategy.open_trade is not None:
            final_price = ltf_data.iloc[-1]['close']
            final_time = ltf_data.index[-1]
            engine.strategy.close_position(final_time, final_price, 'backtest_end')
        
        # Kết quả
        results = engine.strategy.get_performance_metrics()
        print(f"\n📊 KẾT QUẢ SIMPLE STRATEGY:")
        print(f"Tổng giao dịch: {results.get('total_trades', 0)}")
        print(f"Tỷ lệ thắng: {results.get('win_rate', 0)*100:.1f}%")
        print(f"Tổng return: {results.get('total_return', 0)*100:.1f}%")
        print(f"Profit factor: {results.get('profit_factor', 0):.2f}")
        
    else:
        print("❌ Không tìm thấy signals nào")

if __name__ == "__main__":
    test_simple_strategy()
