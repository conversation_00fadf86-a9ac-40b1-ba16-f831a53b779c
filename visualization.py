"""
Module visualization cho kết quả backtest và phân tích
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
from typing import Dict, List
import os
from datetime import datetime

import config
from strategy import Trade

class TradingVisualizer:
    """Class để tạo các biểu đồ phân tích trading"""
    
    def __init__(self):
        # Tạo thư mục plots nếu chưa có
        if config.PLOT_CONFIG['save_plots']:
            os.makedirs(config.PLOT_CONFIG['plot_dir'], exist_ok=True)
        
        # Set style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
    
    def plot_price_with_signals(self, data: Dict[str, pd.DataFrame], trades: List[Trade], 
                               timeframe: str = 'LTF', save: bool = True) -> go.Figure:
        """
        Vẽ biểu đồ giá với tín hiệu giao dịch
        
        Args:
            data: Dict chứa dữ liệu các timeframe
            trades: List các giao dịch
            timeframe: Timeframe để vẽ
            save: Có lưu file không
        """
        if timeframe not in data or data[timeframe].empty:
            print(f"❌ Không có dữ liệu cho {timeframe}")
            return None
        
        df = data[timeframe].copy()
        
        # Tạo subplot
        fig = make_subplots(
            rows=4, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.05,
            subplot_titles=('Price & Signals', 'MACD', 'RSI', 'Volume'),
            row_heights=[0.5, 0.2, 0.15, 0.15]
        )
        
        # 1. Candlestick chart
        fig.add_trace(
            go.Candlestick(
                x=df.index,
                open=df['open'],
                high=df['high'],
                low=df['low'],
                close=df['close'],
                name='BTC/USDT'
            ),
            row=1, col=1
        )
        
        # Thêm moving averages nếu có
        if 'ema_20' in df.columns:
            fig.add_trace(
                go.Scatter(
                    x=df.index,
                    y=df['ema_20'],
                    mode='lines',
                    name='EMA 20',
                    line=dict(color='orange', width=1)
                ),
                row=1, col=1
            )
        
        if 'ema_50' in df.columns:
            fig.add_trace(
                go.Scatter(
                    x=df.index,
                    y=df['ema_50'],
                    mode='lines',
                    name='EMA 50',
                    line=dict(color='blue', width=1)
                ),
                row=1, col=1
            )
        
        # 2. Thêm tín hiệu giao dịch
        for trade in trades:
            if trade.entry_time in df.index:
                # Entry signal
                color = 'green' if trade.side == 'long' else 'red'
                symbol = 'triangle-up' if trade.side == 'long' else 'triangle-down'
                
                fig.add_trace(
                    go.Scatter(
                        x=[trade.entry_time],
                        y=[trade.entry_price],
                        mode='markers',
                        marker=dict(
                            symbol=symbol,
                            size=12,
                            color=color
                        ),
                        name=f'{trade.side.upper()} Entry',
                        showlegend=False
                    ),
                    row=1, col=1
                )
                
                # Exit signal
                if trade.exit_time and trade.exit_time in df.index:
                    exit_color = 'darkgreen' if trade.pnl > 0 else 'darkred'
                    exit_symbol = 'triangle-down' if trade.side == 'long' else 'triangle-up'
                    
                    fig.add_trace(
                        go.Scatter(
                            x=[trade.exit_time],
                            y=[trade.exit_price],
                            mode='markers',
                            marker=dict(
                                symbol=exit_symbol,
                                size=12,
                                color=exit_color
                            ),
                            name=f'{trade.side.upper()} Exit',
                            showlegend=False
                        ),
                        row=1, col=1
                    )
        
        # 3. MACD
        if all(col in df.columns for col in ['macd', 'macd_signal', 'macd_histogram']):
            fig.add_trace(
                go.Scatter(
                    x=df.index,
                    y=df['macd'],
                    mode='lines',
                    name='MACD',
                    line=dict(color='blue')
                ),
                row=2, col=1
            )
            
            fig.add_trace(
                go.Scatter(
                    x=df.index,
                    y=df['macd_signal'],
                    mode='lines',
                    name='Signal',
                    line=dict(color='red')
                ),
                row=2, col=1
            )
            
            # MACD Histogram
            colors = ['green' if x >= 0 else 'red' for x in df['macd_histogram']]
            fig.add_trace(
                go.Bar(
                    x=df.index,
                    y=df['macd_histogram'],
                    name='Histogram',
                    marker_color=colors,
                    opacity=0.6
                ),
                row=2, col=1
            )
        
        # 4. RSI
        if 'rsi' in df.columns:
            fig.add_trace(
                go.Scatter(
                    x=df.index,
                    y=df['rsi'],
                    mode='lines',
                    name='RSI',
                    line=dict(color='purple')
                ),
                row=3, col=1
            )
            
            # RSI levels
            fig.add_hline(y=70, line_dash="dash", line_color="red", row=3, col=1)
            fig.add_hline(y=30, line_dash="dash", line_color="green", row=3, col=1)
            fig.add_hline(y=50, line_dash="dot", line_color="gray", row=3, col=1)
        
        # 5. Volume
        fig.add_trace(
            go.Bar(
                x=df.index,
                y=df['volume'],
                name='Volume',
                marker_color='lightblue',
                opacity=0.7
            ),
            row=4, col=1
        )
        
        # Update layout
        fig.update_layout(
            title=f'Bitcoin Trading Strategy - {timeframe} Timeframe',
            xaxis_rangeslider_visible=False,
            height=800,
            showlegend=True
        )
        
        # Save plot
        if save and config.PLOT_CONFIG['save_plots']:
            filename = f"{config.PLOT_CONFIG['plot_dir']}/price_signals_{timeframe}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
            fig.write_html(filename)
            print(f"💾 Đã lưu biểu đồ: {filename}")
        
        return fig
    
    def plot_equity_curve(self, trades: List[Trade], initial_capital: float, save: bool = True) -> go.Figure:
        """Vẽ đường cong vốn"""
        if not trades:
            print("❌ Không có giao dịch để vẽ equity curve")
            return None
        
        # Tính toán equity curve
        equity = [initial_capital]
        dates = [trades[0].entry_time]
        
        for trade in trades:
            if trade.exit_time:
                equity.append(equity[-1] + trade.pnl)
                dates.append(trade.exit_time)
        
        # Tính drawdown
        peak = equity[0]
        drawdown = []
        
        for eq in equity:
            if eq > peak:
                peak = eq
            dd = (peak - eq) / peak * 100
            drawdown.append(dd)
        
        # Tạo subplot
        fig = make_subplots(
            rows=2, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.1,
            subplot_titles=('Equity Curve', 'Drawdown %'),
            row_heights=[0.7, 0.3]
        )
        
        # Equity curve
        fig.add_trace(
            go.Scatter(
                x=dates,
                y=equity,
                mode='lines',
                name='Equity',
                line=dict(color='blue', width=2)
            ),
            row=1, col=1
        )
        
        # Buy & Hold comparison
        if trades:
            start_price = trades[0].entry_price
            end_price = trades[-1].exit_price if trades[-1].exit_price else trades[-1].entry_price
            buy_hold_return = (end_price / start_price - 1) * initial_capital + initial_capital
            
            fig.add_trace(
                go.Scatter(
                    x=[dates[0], dates[-1]],
                    y=[initial_capital, buy_hold_return],
                    mode='lines',
                    name='Buy & Hold',
                    line=dict(color='gray', dash='dash')
                ),
                row=1, col=1
            )
        
        # Drawdown
        fig.add_trace(
            go.Scatter(
                x=dates,
                y=drawdown,
                mode='lines',
                fill='tonexty',
                name='Drawdown',
                line=dict(color='red'),
                fillcolor='rgba(255,0,0,0.3)'
            ),
            row=2, col=1
        )
        
        fig.update_layout(
            title='Portfolio Performance',
            height=600,
            showlegend=True
        )
        
        fig.update_yaxes(title_text="Portfolio Value ($)", row=1, col=1)
        fig.update_yaxes(title_text="Drawdown (%)", row=2, col=1)
        
        if save and config.PLOT_CONFIG['save_plots']:
            filename = f"{config.PLOT_CONFIG['plot_dir']}/equity_curve_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
            fig.write_html(filename)
            print(f"💾 Đã lưu equity curve: {filename}")
        
        return fig
    
    def plot_trade_analysis(self, trades: List[Trade], save: bool = True):
        """Vẽ phân tích các giao dịch"""
        if not trades:
            print("❌ Không có giao dịch để phân tích")
            return
        
        # Chuẩn bị dữ liệu
        trade_data = []
        for i, trade in enumerate(trades):
            trade_data.append({
                'trade_num': i + 1,
                'side': trade.side,
                'pnl': trade.pnl,
                'pnl_pct': trade.pnl_pct * 100,
                'duration': (trade.exit_time - trade.entry_time).total_seconds() / 3600 if trade.exit_time else 0,
                'entry_price': trade.entry_price,
                'exit_price': trade.exit_price or trade.entry_price
            })
        
        df_trades = pd.DataFrame(trade_data)
        
        # Tạo subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Trade Analysis', fontsize=16)
        
        # 1. PnL per trade
        colors = ['green' if pnl > 0 else 'red' for pnl in df_trades['pnl']]
        axes[0, 0].bar(df_trades['trade_num'], df_trades['pnl'], color=colors, alpha=0.7)
        axes[0, 0].set_title('PnL per Trade')
        axes[0, 0].set_xlabel('Trade Number')
        axes[0, 0].set_ylabel('PnL ($)')
        axes[0, 0].axhline(y=0, color='black', linestyle='-', alpha=0.3)
        
        # 2. PnL distribution
        axes[0, 1].hist(df_trades['pnl'], bins=20, alpha=0.7, color='blue', edgecolor='black')
        axes[0, 1].set_title('PnL Distribution')
        axes[0, 1].set_xlabel('PnL ($)')
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].axvline(x=0, color='red', linestyle='--', alpha=0.7)
        
        # 3. Trade duration
        axes[1, 0].scatter(df_trades['duration'], df_trades['pnl'], 
                          c=colors, alpha=0.7, s=50)
        axes[1, 0].set_title('PnL vs Trade Duration')
        axes[1, 0].set_xlabel('Duration (hours)')
        axes[1, 0].set_ylabel('PnL ($)')
        axes[1, 0].axhline(y=0, color='black', linestyle='-', alpha=0.3)
        
        # 4. Cumulative PnL
        cumulative_pnl = df_trades['pnl'].cumsum()
        axes[1, 1].plot(df_trades['trade_num'], cumulative_pnl, 
                       marker='o', linewidth=2, markersize=4)
        axes[1, 1].set_title('Cumulative PnL')
        axes[1, 1].set_xlabel('Trade Number')
        axes[1, 1].set_ylabel('Cumulative PnL ($)')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save and config.PLOT_CONFIG['save_plots']:
            filename = f"{config.PLOT_CONFIG['plot_dir']}/trade_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            print(f"💾 Đã lưu trade analysis: {filename}")
        
        plt.show()
    
    def create_dashboard(self, data: Dict[str, pd.DataFrame], trades: List[Trade], 
                        results: Dict, initial_capital: float):
        """Tạo dashboard tổng hợp"""
        print("📊 Tạo dashboard tổng hợp...")
        
        # 1. Price chart với signals
        price_fig = self.plot_price_with_signals(data, trades, 'LTF', save=True)
        if price_fig:
            price_fig.show()
        
        # 2. Equity curve
        equity_fig = self.plot_equity_curve(trades, initial_capital, save=True)
        if equity_fig:
            equity_fig.show()
        
        # 3. Trade analysis
        self.plot_trade_analysis(trades, save=True)
        
        print("✅ Dashboard đã được tạo và lưu!")

if __name__ == "__main__":
    # Test visualization
    from backtest import BacktestEngine
    
    engine = BacktestEngine()
    results = engine.run_backtest(lookback_days=180)
    
    if results:
        viz = TradingVisualizer()
        # Cần có dữ liệu và trades để test
        print("Visualization module ready!")
