"""
Module t<PERSON>h toán các chỉ báo kỹ thuật cho Bitcoin trading
"""

import pandas as pd
import numpy as np
import ta
from typing import Tuple, Dict
import config

class TechnicalIndicators:
    """Class tính toán các chỉ báo kỹ thuật"""
    
    @staticmethod
    def calculate_macd(df: pd.DataFrame, fast: int = 12, slow: int = 26, signal: int = 9) -> pd.DataFrame:
        """
        Tính toán MACD
        
        Args:
            df: DataFrame với cột 'close'
            fast: EMA nhanh
            slow: EMA chậm  
            signal: Signal line
            
        Returns:
            DataFrame với MACD, Signal, Histogram
        """
        result = df.copy()
        
        # Tính MACD
        macd_line = ta.trend.MACD(close=df['close'], window_fast=fast, window_slow=slow, window_sign=signal)
        
        result['macd'] = macd_line.macd()
        result['macd_signal'] = macd_line.macd_signal()
        result['macd_histogram'] = macd_line.macd_diff()
        
        # MACD crossover signals
        result['macd_bullish_cross'] = (
            (result['macd'] > result['macd_signal']) & 
            (result['macd'].shift(1) <= result['macd_signal'].shift(1))
        )
        
        result['macd_bearish_cross'] = (
            (result['macd'] < result['macd_signal']) & 
            (result['macd'].shift(1) >= result['macd_signal'].shift(1))
        )
        
        return result
    
    @staticmethod
    def calculate_rsi(df: pd.DataFrame, period: int = 14) -> pd.DataFrame:
        """
        Tính toán RSI
        
        Args:
            df: DataFrame với cột 'close'
            period: Chu kỳ RSI
            
        Returns:
            DataFrame với RSI và các signal
        """
        result = df.copy()
        
        # Tính RSI
        result['rsi'] = ta.momentum.RSIIndicator(close=df['close'], window=period).rsi()
        
        # RSI signals
        result['rsi_oversold'] = result['rsi'] < config.RSI_CONFIG['oversold']
        result['rsi_overbought'] = result['rsi'] > config.RSI_CONFIG['overbought']
        
        # RSI từ oversold/overbought
        result['rsi_from_oversold'] = (
            (result['rsi'] > config.RSI_CONFIG['oversold']) & 
            (result['rsi'].shift(1) <= config.RSI_CONFIG['oversold'])
        )
        
        result['rsi_from_overbought'] = (
            (result['rsi'] < config.RSI_CONFIG['overbought']) & 
            (result['rsi'].shift(1) >= config.RSI_CONFIG['overbought'])
        )
        
        return result
    
    @staticmethod
    def calculate_moving_averages(df: pd.DataFrame, periods: list = [20, 50, 200]) -> pd.DataFrame:
        """Tính toán các đường trung bình động"""
        result = df.copy()
        
        for period in periods:
            result[f'sma_{period}'] = ta.trend.SMAIndicator(close=df['close'], window=period).sma_indicator()
            result[f'ema_{period}'] = ta.trend.EMAIndicator(close=df['close'], window=period).ema_indicator()
        
        return result
    
    @staticmethod
    def calculate_atr(df: pd.DataFrame, period: int = 14) -> pd.DataFrame:
        """Tính toán Average True Range"""
        result = df.copy()
        result['atr'] = ta.volatility.AverageTrueRange(
            high=df['high'], 
            low=df['low'], 
            close=df['close'], 
            window=period
        ).average_true_range()
        
        return result
    
    @staticmethod
    def calculate_bollinger_bands(df: pd.DataFrame, period: int = 20, std: int = 2) -> pd.DataFrame:
        """Tính toán Bollinger Bands"""
        result = df.copy()
        
        bb = ta.volatility.BollingerBands(close=df['close'], window=period, window_dev=std)
        result['bb_upper'] = bb.bollinger_hband()
        result['bb_middle'] = bb.bollinger_mavg()
        result['bb_lower'] = bb.bollinger_lband()
        result['bb_width'] = bb.bollinger_wband()
        
        return result
    
    @staticmethod
    def calculate_volume_indicators(df: pd.DataFrame) -> pd.DataFrame:
        """Tính toán các chỉ báo volume"""
        result = df.copy()

        # Volume SMA (manual calculation)
        result['volume_sma'] = result['volume'].rolling(window=20).mean()

        # On Balance Volume
        try:
            result['obv'] = ta.volume.OnBalanceVolumeIndicator(
                close=df['close'],
                volume=df['volume']
            ).on_balance_volume()
        except:
            # Manual OBV calculation if ta version doesn't support it
            obv = [0]
            for i in range(1, len(df)):
                if df['close'].iloc[i] > df['close'].iloc[i-1]:
                    obv.append(obv[-1] + df['volume'].iloc[i])
                elif df['close'].iloc[i] < df['close'].iloc[i-1]:
                    obv.append(obv[-1] - df['volume'].iloc[i])
                else:
                    obv.append(obv[-1])
            result['obv'] = obv

        # Volume above average
        result['volume_above_avg'] = result['volume'] > result['volume_sma']

        return result
    
    @staticmethod
    def calculate_all_indicators(df: pd.DataFrame) -> pd.DataFrame:
        """Tính toán tất cả chỉ báo"""
        result = df.copy()
        
        # MACD
        result = TechnicalIndicators.calculate_macd(
            result, 
            config.MACD_CONFIG['fast'],
            config.MACD_CONFIG['slow'], 
            config.MACD_CONFIG['signal']
        )
        
        # RSI
        result = TechnicalIndicators.calculate_rsi(result, config.RSI_CONFIG['period'])
        
        # Moving Averages
        result = TechnicalIndicators.calculate_moving_averages(result)
        
        # ATR
        result = TechnicalIndicators.calculate_atr(result)
        
        # Bollinger Bands
        result = TechnicalIndicators.calculate_bollinger_bands(result)
        
        # Volume indicators
        result = TechnicalIndicators.calculate_volume_indicators(result)
        
        return result

class MultiTimeframeAnalysis:
    """Phân tích đa khung thời gian"""
    
    @staticmethod
    def analyze_trend(df: pd.DataFrame) -> str:
        """
        Phân tích xu hướng dựa trên MACD và MA
        
        Returns:
            'bullish', 'bearish', 'neutral'
        """
        if df.empty or len(df) < 50:
            return 'neutral'
        
        latest = df.iloc[-1]
        
        # Kiểm tra MACD trend
        macd_bullish = latest['macd'] > latest['macd_signal']
        
        # Kiểm tra MA trend
        if 'ema_20' in df.columns and 'ema_50' in df.columns:
            ma_bullish = latest['ema_20'] > latest['ema_50']
        else:
            ma_bullish = True  # Default nếu không có MA
        
        # Kiểm tra price vs MA
        price_above_ma = latest['close'] > latest.get('ema_20', latest['close'])
        
        if macd_bullish and ma_bullish and price_above_ma:
            return 'bullish'
        elif not macd_bullish and not ma_bullish and not price_above_ma:
            return 'bearish'
        else:
            return 'neutral'
    
    @staticmethod
    def get_mtf_signals(data: Dict[str, pd.DataFrame]) -> Dict[str, any]:
        """
        Phân tích tín hiệu đa khung thời gian
        
        Args:
            data: Dict với HTF, ITF, LTF DataFrames
            
        Returns:
            Dict với các tín hiệu và phân tích
        """
        signals = {
            'htf_trend': 'neutral',
            'itf_setup': False,
            'ltf_entry': False,
            'signal_type': 'none',  # 'long', 'short', 'none'
            'confidence': 0.0
        }
        
        # Phân tích HTF trend
        if 'HTF' in data and not data['HTF'].empty:
            signals['htf_trend'] = MultiTimeframeAnalysis.analyze_trend(data['HTF'])
        
        # Phân tích ITF setup
        if 'ITF' in data and not data['ITF'].empty:
            itf_latest = data['ITF'].iloc[-1]
            
            # Setup cho Long
            itf_long_setup = (
                itf_latest.get('rsi_from_oversold', False) and
                itf_latest.get('macd_bullish_cross', False)
            )
            
            # Setup cho Short  
            itf_short_setup = (
                itf_latest.get('rsi_from_overbought', False) and
                itf_latest.get('macd_bearish_cross', False)
            )
            
            signals['itf_setup'] = itf_long_setup or itf_short_setup
            
            if itf_long_setup and signals['htf_trend'] in ['bullish', 'neutral']:
                signals['signal_type'] = 'long'
            elif itf_short_setup and signals['htf_trend'] in ['bearish', 'neutral']:
                signals['signal_type'] = 'short'
        
        # Phân tích LTF entry
        if 'LTF' in data and not data['LTF'].empty and signals['signal_type'] != 'none':
            ltf_latest = data['LTF'].iloc[-1]
            
            # Xác nhận với volume
            volume_confirm = ltf_latest.get('volume_above_avg', False)
            
            if signals['signal_type'] == 'long':
                ltf_confirm = (
                    ltf_latest.get('macd', 0) > ltf_latest.get('macd_signal', 0) and
                    volume_confirm
                )
            else:  # short
                ltf_confirm = (
                    ltf_latest.get('macd', 0) < ltf_latest.get('macd_signal', 0) and
                    volume_confirm
                )
            
            signals['ltf_entry'] = ltf_confirm
        
        # Tính confidence score (điều chỉnh để dễ trigger hơn)
        confidence_factors = []
        if signals['htf_trend'] != 'neutral':
            confidence_factors.append(0.3)  # Giảm từ 0.4
        else:
            confidence_factors.append(0.1)  # Thêm điểm cho neutral

        if signals['itf_setup']:
            confidence_factors.append(0.4)
        if signals['ltf_entry']:
            confidence_factors.append(0.3)  # Tăng từ 0.2

        # Bonus nếu có signal type
        if signals['signal_type'] != 'none':
            confidence_factors.append(0.1)

        signals['confidence'] = sum(confidence_factors)
        
        return signals

if __name__ == "__main__":
    # Test indicators
    from data_fetcher import DataFetcher
    
    fetcher = DataFetcher()
    df = fetcher.fetch_ohlcv(config.SYMBOL, '1h', 200)
    
    if not df.empty:
        # Test tính toán indicators
        df_with_indicators = TechnicalIndicators.calculate_all_indicators(df)
        print("Indicators calculated:")
        print(df_with_indicators.columns.tolist())
        print("\nLatest values:")
        print(df_with_indicators.iloc[-1][['close', 'macd', 'macd_signal', 'rsi']])
