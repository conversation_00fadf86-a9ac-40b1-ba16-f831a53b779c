"""
Quick test để demo tín hiệu real-time
"""

import time
import pandas as pd
from datetime import datetime
import ccxt
from basic_strategy import BasicMacdStrategy
from indicators import TechnicalIndicators
import config

def quick_signal_check():
    """Kiểm tra tín hiệu nhanh"""
    
    print("🔍 QUICK SIGNAL CHECK")
    print("=" * 30)
    
    try:
        # Lấy dữ liệu
        exchange = ccxt.binance({'enableRateLimit': True})
        
        print("📊 Lấy dữ liệu từ Binance...")
        data_4h = exchange.fetch_ohlcv(config.SYMBOL, '4h', limit=100)
        data_1h = exchange.fetch_ohlcv(config.SYMBOL, '1h', limit=100)
        
        # Convert to DataFrame
        df_4h = pd.DataFrame(data_4h, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        df_1h = pd.DataFrame(data_1h, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
        
        for df in [df_4h, df_1h]:
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            df[['open', 'high', 'low', 'close', 'volume']] = df[['open', 'high', 'low', 'close', 'volume']].astype(float)
        
        # Tính indicators
        print("🔧 Tính toán indicators...")
        df_4h_indicators = TechnicalIndicators.calculate_all_indicators(df_4h)
        df_1h_indicators = TechnicalIndicators.calculate_all_indicators(df_1h)
        
        data = {
            'ITF': df_4h_indicators,
            'LTF': df_1h_indicators
        }
        
        # Check signal
        strategy = BasicMacdStrategy(10000)
        signal = strategy.check_entry_signals(data)
        
        # Hiển thị kết quả
        current_price = df_1h_indicators['close'].iloc[-1]
        itf_latest = df_4h_indicators.iloc[-1]
        
        print(f"\n📊 MARKET STATUS:")
        print(f"💰 BTC Price: ${current_price:,.2f}")
        print(f"📈 MACD (4H): {itf_latest.get('macd', 0):.2f}")
        print(f"📊 Signal (4H): {itf_latest.get('macd_signal', 0):.2f}")
        print(f"📈 RSI (4H): {itf_latest.get('rsi', 50):.1f}")
        
        if signal:
            print(f"\n🎯 SIGNAL DETECTED: {signal.upper()}!")
            
            # Tính stop loss và take profit
            atr = df_1h_indicators.iloc[-1].get('atr', current_price * 0.02)
            
            if signal == 'long':
                stop_loss = current_price - (atr * 2)
                take_profit = current_price + (atr * 4)
                emoji = "🟢"
            else:
                stop_loss = current_price + (atr * 2)
                take_profit = current_price - (atr * 4)
                emoji = "🔴"
            
            risk = abs(current_price - stop_loss)
            reward = abs(take_profit - current_price)
            rr_ratio = reward / risk if risk > 0 else 0
            
            print(f"\n{emoji} TRADING SIGNAL:")
            print(f"   Direction: {signal.upper()}")
            print(f"   Entry: ${current_price:,.2f}")
            print(f"   Stop Loss: ${stop_loss:,.2f}")
            print(f"   Take Profit: ${take_profit:,.2f}")
            print(f"   Risk/Reward: 1:{rr_ratio:.1f}")
            print(f"   Time: {datetime.now().strftime('%H:%M:%S')}")
            
        else:
            print(f"\n⏳ NO SIGNAL - Waiting for MACD crossover...")
            
            # Hiển thị khoảng cách đến signal
            macd_diff = itf_latest.get('macd', 0) - itf_latest.get('macd_signal', 0)
            if macd_diff > 0:
                print(f"   📈 MACD above signal (+{macd_diff:.2f}) - Bullish bias")
            else:
                print(f"   📉 MACD below signal ({macd_diff:.2f}) - Bearish bias")
        
        return signal
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def demo_signals():
    """Demo tín hiệu liên tục"""
    
    print("🎬 DEMO REAL-TIME SIGNALS")
    print("=" * 35)
    print("⏰ Check mỗi 30 giây (Ctrl+C để dừng)")
    print()
    
    signal_count = 0
    
    while True:
        try:
            print(f"🔍 Check #{signal_count + 1} - {datetime.now().strftime('%H:%M:%S')}")
            
            signal = quick_signal_check()
            
            if signal:
                signal_count += 1
                print(f"\n🎉 Total signals found: {signal_count}")
            
            print(f"\n⏳ Next check in 30 seconds...")
            print("-" * 50)
            
            time.sleep(30)
            
        except KeyboardInterrupt:
            print(f"\n🛑 Demo stopped! Total signals: {signal_count}")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
            time.sleep(10)

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        demo_signals()
    else:
        quick_signal_check()
