"""
Chi<PERSON>n lư<PERSON>c giao dịch MACD + RSI Multi-Timeframe cho Bitcoin
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime
import config
from indicators import TechnicalIndicators, MultiTimeframeAnalysis

@dataclass
class Trade:
    """Class đại diện cho một giao dịch"""
    entry_time: datetime
    entry_price: float
    exit_time: Optional[datetime] = None
    exit_price: Optional[float] = None
    side: str = 'long'  # 'long' or 'short'
    size: float = 0.0
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    pnl: float = 0.0
    pnl_pct: float = 0.0
    status: str = 'open'  # 'open', 'closed', 'stopped'
    
    def close_trade(self, exit_time: datetime, exit_price: float, reason: str = 'signal'):
        """Đóng giao dịch"""
        self.exit_time = exit_time
        self.exit_price = exit_price
        self.status = 'closed'
        
        if self.side == 'long':
            self.pnl_pct = (exit_price - self.entry_price) / self.entry_price
        else:  # short
            self.pnl_pct = (self.entry_price - exit_price) / self.entry_price
            
        self.pnl = self.pnl_pct * self.size

class MacdRsiStrategy:
    """Chiến lược MACD + RSI Multi-Timeframe"""
    
    def __init__(self, initial_capital: float = 10000):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.trades: List[Trade] = []
        self.open_trade: Optional[Trade] = None
        self.risk_per_trade = config.RISK_CONFIG['max_risk_per_trade']
        self.risk_reward_ratio = config.RISK_CONFIG['risk_reward_ratio']
        
    def calculate_position_size(self, entry_price: float, stop_loss: float) -> float:
        """
        Tính toán kích thước vị thế dựa trên risk management
        
        Args:
            entry_price: Giá vào lệnh
            stop_loss: Giá stop loss
            
        Returns:
            Kích thước vị thế (USD)
        """
        risk_amount = self.current_capital * self.risk_per_trade
        price_diff = abs(entry_price - stop_loss)
        
        if price_diff == 0:
            return 0
            
        position_size = risk_amount / (price_diff / entry_price)
        
        # Giới hạn không quá 50% vốn
        max_position = self.current_capital * 0.5
        return min(position_size, max_position)
    
    def calculate_stop_loss(self, df: pd.DataFrame, side: str, entry_price: float) -> float:
        """Tính toán stop loss dựa trên ATR"""
        if 'atr' not in df.columns or df['atr'].isna().all():
            # Fallback: 2% stop loss
            if side == 'long':
                return entry_price * 0.98
            else:
                return entry_price * 1.02
        
        atr = df['atr'].iloc[-1]
        multiplier = config.RISK_CONFIG['stop_loss_atr_multiplier']
        
        if side == 'long':
            return entry_price - (atr * multiplier)
        else:
            return entry_price + (atr * multiplier)
    
    def calculate_take_profit(self, entry_price: float, stop_loss: float, side: str) -> float:
        """Tính toán take profit dựa trên risk/reward ratio"""
        risk = abs(entry_price - stop_loss)
        reward = risk * self.risk_reward_ratio
        
        if side == 'long':
            return entry_price + reward
        else:
            return entry_price - reward
    
    def check_entry_signals(self, data: Dict[str, pd.DataFrame]) -> Optional[str]:
        """
        Kiểm tra tín hiệu vào lệnh
        
        Args:
            data: Dict chứa dữ liệu các timeframe
            
        Returns:
            'long', 'short', hoặc None
        """
        if self.open_trade is not None:
            return None  # Đã có lệnh mở
        
        # Phân tích multi-timeframe
        signals = MultiTimeframeAnalysis.get_mtf_signals(data)
        
        # Yêu cầu confidence tối thiểu 0.6 (giảm từ 0.8 để dễ trigger hơn)
        if signals['confidence'] < 0.6:
            return None
        
        # Kiểm tra tín hiệu entry
        if (signals['signal_type'] == 'long' and 
            signals['htf_trend'] in ['bullish', 'neutral'] and
            signals['itf_setup'] and 
            signals['ltf_entry']):
            return 'long'
        
        elif (signals['signal_type'] == 'short' and 
              signals['htf_trend'] in ['bearish', 'neutral'] and
              signals['itf_setup'] and 
              signals['ltf_entry']):
            return 'short'
        
        return None
    
    def check_exit_signals(self, data: Dict[str, pd.DataFrame]) -> bool:
        """Kiểm tra tín hiệu thoát lệnh"""
        if self.open_trade is None:
            return False
        
        # Kiểm tra trên ITF cho exit signals
        if 'ITF' not in data or data['ITF'].empty:
            return False
        
        itf_latest = data['ITF'].iloc[-1]
        
        if self.open_trade.side == 'long':
            # Exit long khi MACD bearish cross hoặc RSI overbought
            exit_signal = (
                itf_latest.get('macd_bearish_cross', False) or
                (itf_latest.get('rsi', 50) > config.RSI_CONFIG['overbought'] and
                 itf_latest.get('macd', 0) < itf_latest.get('macd_signal', 0))
            )
        else:  # short
            # Exit short khi MACD bullish cross hoặc RSI oversold
            exit_signal = (
                itf_latest.get('macd_bullish_cross', False) or
                (itf_latest.get('rsi', 50) < config.RSI_CONFIG['oversold'] and
                 itf_latest.get('macd', 0) > itf_latest.get('macd_signal', 0))
            )
        
        return exit_signal
    
    def open_position(self, timestamp: datetime, price: float, side: str, ltf_data: pd.DataFrame):
        """Mở vị thế mới"""
        if self.open_trade is not None:
            return  # Đã có lệnh mở
        
        # Tính toán stop loss và take profit
        stop_loss = self.calculate_stop_loss(ltf_data, side, price)
        take_profit = self.calculate_take_profit(price, stop_loss, side)
        
        # Tính toán kích thước vị thế
        position_size = self.calculate_position_size(price, stop_loss)
        
        if position_size <= 0:
            return
        
        # Tạo trade mới
        self.open_trade = Trade(
            entry_time=timestamp,
            entry_price=price,
            side=side,
            size=position_size,
            stop_loss=stop_loss,
            take_profit=take_profit
        )
        
        print(f"🔵 Mở {side} tại {price:.2f} | SL: {stop_loss:.2f} | TP: {take_profit:.2f} | Size: ${position_size:.2f}")
    
    def close_position(self, timestamp: datetime, price: float, reason: str = 'signal'):
        """Đóng vị thế"""
        if self.open_trade is None:
            return
        
        # Đóng trade
        self.open_trade.close_trade(timestamp, price, reason)
        
        # Cập nhật vốn
        self.current_capital += self.open_trade.pnl
        
        # Lưu trade
        self.trades.append(self.open_trade)
        
        print(f"🔴 Đóng {self.open_trade.side} tại {price:.2f} | PnL: ${self.open_trade.pnl:.2f} ({self.open_trade.pnl_pct*100:.2f}%) | Reason: {reason}")
        
        self.open_trade = None
    
    def check_stop_loss_take_profit(self, timestamp: datetime, high: float, low: float):
        """Kiểm tra stop loss và take profit"""
        if self.open_trade is None:
            return
        
        if self.open_trade.side == 'long':
            # Kiểm tra stop loss
            if low <= self.open_trade.stop_loss:
                self.close_position(timestamp, self.open_trade.stop_loss, 'stop_loss')
                return
            
            # Kiểm tra take profit
            if high >= self.open_trade.take_profit:
                self.close_position(timestamp, self.open_trade.take_profit, 'take_profit')
                return
        
        else:  # short
            # Kiểm tra stop loss
            if high >= self.open_trade.stop_loss:
                self.close_position(timestamp, self.open_trade.stop_loss, 'stop_loss')
                return
            
            # Kiểm tra take profit
            if low <= self.open_trade.take_profit:
                self.close_position(timestamp, self.open_trade.take_profit, 'take_profit')
                return
    
    def get_performance_metrics(self) -> Dict:
        """Tính toán các metrics hiệu suất"""
        if not self.trades:
            return {}
        
        # Tính toán các metrics cơ bản
        total_trades = len(self.trades)
        winning_trades = len([t for t in self.trades if t.pnl > 0])
        losing_trades = len([t for t in self.trades if t.pnl < 0])
        
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        total_pnl = sum(t.pnl for t in self.trades)
        total_return = (self.current_capital - self.initial_capital) / self.initial_capital
        
        # Tính drawdown
        equity_curve = [self.initial_capital]
        for trade in self.trades:
            equity_curve.append(equity_curve[-1] + trade.pnl)
        
        peak = equity_curve[0]
        max_drawdown = 0
        for equity in equity_curve:
            if equity > peak:
                peak = equity
            drawdown = (peak - equity) / peak
            max_drawdown = max(max_drawdown, drawdown)
        
        # Profit factor
        gross_profit = sum(t.pnl for t in self.trades if t.pnl > 0)
        gross_loss = abs(sum(t.pnl for t in self.trades if t.pnl < 0))
        profit_factor = gross_profit / gross_loss if gross_loss > 0 else float('inf')
        
        # Average trade
        avg_trade = total_pnl / total_trades if total_trades > 0 else 0
        avg_win = gross_profit / winning_trades if winning_trades > 0 else 0
        avg_loss = gross_loss / losing_trades if losing_trades > 0 else 0
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'win_rate': win_rate,
            'total_return': total_return,
            'total_pnl': total_pnl,
            'max_drawdown': max_drawdown,
            'profit_factor': profit_factor,
            'avg_trade': avg_trade,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'final_capital': self.current_capital
        }
