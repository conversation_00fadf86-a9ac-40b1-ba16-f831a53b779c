"""
Main script để chạy backtest chiến lược MACD + RSI Multi-Timeframe cho Bitcoin
"""

import argparse
import sys
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from backtest import BacktestEngine
from visualization import TradingVisualizer
from data_fetcher import DataFetcher
from indicators import MultiTimeframeAnalysis
from basic_strategy import BasicMacdStrategy
import config

def main():
    """Hàm main"""
    parser = argparse.ArgumentParser(description='Bitcoin MACD + RSI Multi-Timeframe Strategy Backtest')
    
    parser.add_argument('--symbol', type=str, default=config.SYMBOL,
                       help='Trading pair (default: BTC/USDT)')
    parser.add_argument('--days', type=int, default=config.BACKTEST_CONFIG['lookback_days'],
                       help='Number of days to backtest (default: 365)')
    parser.add_argument('--capital', type=float, default=config.BACKTEST_CONFIG['initial_capital'],
                       help='Initial capital in USD (default: 10000)')
    parser.add_argument('--no-plots', action='store_true',
                       help='Skip generating plots')
    parser.add_argument('--live-analysis', action='store_true',
                       help='Run live market analysis instead of backtest')
    
    args = parser.parse_args()
    
    print("🚀 BITCOIN MACD + RSI MULTI-TIMEFRAME STRATEGY")
    print("=" * 60)
    print(f"📊 Symbol: {args.symbol}")
    print(f"💰 Capital: ${args.capital:,.2f}")
    print(f"📅 Lookback: {args.days} days")
    print("=" * 60)
    
    if args.live_analysis:
        run_live_analysis(args.symbol)
    else:
        run_backtest(args.symbol, args.days, args.capital, not args.no_plots)

def run_backtest(symbol: str, days: int, capital: float, create_plots: bool = True):
    """Chạy backtest"""
    try:
        # Khởi tạo backtest engine với basic strategy
        engine = BacktestEngine(initial_capital=capital)
        engine.strategy = BasicMacdStrategy(capital)  # Sử dụng basic strategy

        # Chạy backtest
        results = engine.run_backtest(symbol=symbol, lookback_days=days)
        
        if not results:
            print("❌ Backtest thất bại!")
            return
        
        # In kết quả
        engine.print_results(results)
        
        # Tạo visualization nếu được yêu cầu
        if create_plots and results.get('total_trades', 0) > 0:
            print("\n📊 Tạo biểu đồ phân tích...")
            
            # Lấy lại dữ liệu để vẽ biểu đồ
            data = engine.prepare_data(symbol, days)
            trades = engine.strategy.trades
            
            if data and trades:
                viz = TradingVisualizer()
                viz.create_dashboard(data, trades, results, capital)
            else:
                print("⚠️ Không thể tạo biểu đồ - thiếu dữ liệu")
        
        # Lưu kết quả
        save_results(results, symbol)
        
    except KeyboardInterrupt:
        print("\n⏹️ Đã dừng backtest")
    except Exception as e:
        print(f"❌ Lỗi trong quá trình backtest: {e}")
        import traceback
        traceback.print_exc()

def run_live_analysis(symbol: str):
    """Chạy phân tích thị trường real-time"""
    print(f"\n🔴 PHÂN TÍCH THỜI GIAN THỰC - {symbol}")
    print("=" * 50)
    
    try:
        # Lấy dữ liệu hiện tại
        fetcher = DataFetcher()
        data = fetcher.fetch_multiple_timeframes(symbol, config.TIMEFRAMES, 200)
        
        if not data:
            print("❌ Không thể lấy dữ liệu")
            return
        
        # Tính toán indicators
        from indicators import TechnicalIndicators
        for tf_name, df in data.items():
            data[tf_name] = TechnicalIndicators.calculate_all_indicators(df)
        
        # Phân tích tín hiệu
        signals = MultiTimeframeAnalysis.get_mtf_signals(data)
        
        # Hiển thị kết quả
        print(f"📊 Phân tích tại: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"💰 Giá hiện tại: ${data['LTF']['close'].iloc[-1]:,.2f}")
        
        print(f"\n🔍 PHÂN TÍCH ĐA KHUNG THỜI GIAN:")
        print(f"   • HTF Trend (Daily): {signals['htf_trend'].upper()}")
        print(f"   • ITF Setup (4H): {'✅' if signals['itf_setup'] else '❌'}")
        print(f"   • LTF Entry (1H): {'✅' if signals['ltf_entry'] else '❌'}")
        print(f"   • Signal Type: {signals['signal_type'].upper()}")
        print(f"   • Confidence: {signals['confidence']*100:.1f}%")
        
        # Hiển thị chi tiết indicators
        print(f"\n📈 CHỈ BÁO CHI TIẾT:")
        
        for tf_name, df in data.items():
            if df.empty:
                continue
                
            latest = df.iloc[-1]
            tf_display = {'HTF': 'Daily', 'ITF': '4H', 'LTF': '1H'}
            
            print(f"\n   {tf_display.get(tf_name, tf_name)}:")
            print(f"      MACD: {latest.get('macd', 0):.4f} | Signal: {latest.get('macd_signal', 0):.4f}")
            print(f"      RSI: {latest.get('rsi', 0):.2f}")
            
            if 'ema_20' in latest:
                print(f"      EMA20: ${latest['ema_20']:,.2f}")
            if 'volume_above_avg' in latest:
                print(f"      Volume: {'Above Avg' if latest['volume_above_avg'] else 'Below Avg'}")
        
        # Khuyến nghị
        print(f"\n💡 KHUYẾN NGHỊ:")
        if signals['confidence'] >= 0.8:
            if signals['signal_type'] == 'long':
                print("   🟢 TÍNH HIỆU LONG MẠNH - Cân nhắc mua")
            elif signals['signal_type'] == 'short':
                print("   🔴 TÍN HIỆU SHORT MẠNH - Cân nhắc bán")
        elif signals['confidence'] >= 0.6:
            print("   🟡 TÍN HIỆU TRUNG BÌNH - Chờ xác nhận thêm")
        else:
            print("   ⚪ KHÔNG CÓ TÍN HIỆU RÕ RÀNG - Đứng ngoài")
        
        print(f"\n⚠️ LƯU Ý: Đây chỉ là phân tích kỹ thuật, không phải lời khuyên đầu tư!")
        
    except Exception as e:
        print(f"❌ Lỗi phân tích live: {e}")

def save_results(results: dict, symbol: str):
    """Lưu kết quả backtest"""
    try:
        import json
        import os
        
        # Tạo thư mục results
        os.makedirs('results', exist_ok=True)
        
        # Chuẩn bị dữ liệu để lưu
        save_data = results.copy()
        
        # Convert datetime objects to strings
        for key, value in save_data.items():
            if hasattr(value, 'strftime'):
                save_data[key] = value.strftime('%Y-%m-%d %H:%M:%S')
        
        # Tạo filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"results/backtest_{symbol.replace('/', '_')}_{timestamp}.json"
        
        # Lưu file
        with open(filename, 'w') as f:
            json.dump(save_data, f, indent=2, default=str)
        
        print(f"💾 Đã lưu kết quả: {filename}")
        
    except Exception as e:
        print(f"⚠️ Không thể lưu kết quả: {e}")

def check_dependencies():
    """Kiểm tra các dependencies cần thiết"""
    required_packages = ['ccxt', 'pandas', 'numpy', 'ta', 'matplotlib', 'plotly']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Thiếu các package sau:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n💡 Chạy lệnh sau để cài đặt:")
        print("pip install -r requirements.txt")
        return False
    
    return True

if __name__ == "__main__":
    # Kiểm tra dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Chạy main
    main()
