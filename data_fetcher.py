"""
Module để thu thập dữ liệu Bitcoin từ các sàn giao dịch
"""

import ccxt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
from typing import Dict, List, Optional
import config

class DataFetcher:
    """Class để thu thập dữ liệu từ exchange"""
    
    def __init__(self, exchange_name: str = config.EXCHANGE):
        """
        Khởi tạo DataFetcher
        
        Args:
            exchange_name: Tên exchange (binance, coinbase, kraken, etc.)
        """
        self.exchange_name = exchange_name
        self.exchange = self._init_exchange()
        
    def _init_exchange(self):
        """Khởi tạo exchange object"""
        try:
            exchange_class = getattr(ccxt, self.exchange_name)
            exchange = exchange_class({
                'apiKey': '',  # Không cần API key cho public data
                'secret': '',
                'timeout': 30000,
                'enableRateLimit': True,
            })
            return exchange
        except Exception as e:
            print(f"Lỗi khởi tạo exchange {self.exchange_name}: {e}")
            # Fallback to binance
            return ccxt.binance({'enableRateLimit': True})
    
    def fetch_ohlcv(self, symbol: str, timeframe: str, limit: int = 1000) -> pd.DataFrame:
        """
        Lấy dữ liệu OHLCV
        
        Args:
            symbol: Cặp giao dịch (BTC/USDT)
            timeframe: Khung thời gian (1h, 4h, 1d)
            limit: Số nến muốn lấy
            
        Returns:
            DataFrame với columns: timestamp, open, high, low, close, volume
        """
        try:
            print(f"Đang lấy dữ liệu {symbol} {timeframe}...")
            
            # Lấy dữ liệu từ exchange
            ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            
            # Chuyển đổi thành DataFrame
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # Chuyển đổi timestamp
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            # Đảm bảo kiểu dữ liệu số
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            df[numeric_columns] = df[numeric_columns].astype(float)
            
            print(f"✅ Đã lấy {len(df)} nến dữ liệu {timeframe}")
            return df
            
        except Exception as e:
            print(f"❌ Lỗi lấy dữ liệu {symbol} {timeframe}: {e}")
            return pd.DataFrame()
    
    def fetch_multiple_timeframes(self, symbol: str, timeframes: Dict[str, str], limit: int = 1000) -> Dict[str, pd.DataFrame]:
        """
        Lấy dữ liệu cho nhiều khung thời gian
        
        Args:
            symbol: Cặp giao dịch
            timeframes: Dict mapping tên -> timeframe
            limit: Số nến cho mỗi timeframe
            
        Returns:
            Dict mapping tên timeframe -> DataFrame
        """
        data = {}
        
        for name, tf in timeframes.items():
            df = self.fetch_ohlcv(symbol, tf, limit)
            if not df.empty:
                data[name] = df
                # Delay để tránh rate limit
                time.sleep(0.5)
            else:
                print(f"⚠️ Không thể lấy dữ liệu cho {name} ({tf})")
        
        return data
    
    def get_latest_price(self, symbol: str) -> Optional[float]:
        """Lấy giá hiện tại"""
        try:
            ticker = self.exchange.fetch_ticker(symbol)
            return ticker['last']
        except Exception as e:
            print(f"Lỗi lấy giá hiện tại: {e}")
            return None
    
    def validate_data(self, df: pd.DataFrame) -> bool:
        """Kiểm tra tính hợp lệ của dữ liệu"""
        if df.empty:
            return False
            
        # Kiểm tra missing values
        if df.isnull().any().any():
            print("⚠️ Dữ liệu có giá trị null")
            return False
            
        # Kiểm tra giá trị âm
        if (df[['open', 'high', 'low', 'close', 'volume']] < 0).any().any():
            print("⚠️ Dữ liệu có giá trị âm")
            return False
            
        # Kiểm tra logic OHLC
        invalid_ohlc = (
            (df['high'] < df['low']) |
            (df['high'] < df['open']) |
            (df['high'] < df['close']) |
            (df['low'] > df['open']) |
            (df['low'] > df['close'])
        )
        
        if invalid_ohlc.any():
            print("⚠️ Dữ liệu OHLC không hợp lệ")
            return False
            
        return True

if __name__ == "__main__":
    # Test DataFetcher
    fetcher = DataFetcher()
    
    # Test lấy dữ liệu single timeframe
    df = fetcher.fetch_ohlcv(config.SYMBOL, '1h', 100)
    print(f"Dữ liệu 1h: {len(df)} rows")
    print(df.head())
    
    # Test lấy dữ liệu multiple timeframes
    data = fetcher.fetch_multiple_timeframes(config.SYMBOL, config.TIMEFRAMES, 500)
    for name, df in data.items():
        print(f"{name}: {len(df)} rows")
