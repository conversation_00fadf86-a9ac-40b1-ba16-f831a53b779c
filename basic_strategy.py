"""
<PERSON><PERSON><PERSON> lư<PERSON><PERSON> cơ bản chỉ dựa trên MACD crossover
"""

import pandas as pd
from typing import Dict, Optional
from datetime import datetime
from strategy import MacdRsiStrategy
import config

class BasicMacdStrategy(MacdRsiStrategy):
    """Chiến lư<PERSON> cơ bản chỉ dùng MACD crossover"""
    
    def check_entry_signals(self, data: Dict[str, pd.DataFrame]) -> Optional[str]:
        """
        Kiểm tra tín hiệu vào lệnh chỉ dựa trên MACD cross
        """
        if self.open_trade is not None:
            return None  # Đã có lệnh mở
        
        # Chỉ cần ITF data
        if 'ITF' not in data or data['ITF'].empty or len(data['ITF']) < 2:
            return None
        
        itf_data = data['ITF']
        current = itf_data.iloc[-1]
        previous = itf_data.iloc[-2]
        
        # MACD Bullish Cross (MACD cắt lên trên Signal)
        macd_bullish_cross = (
            current.get('macd', 0) > current.get('macd_signal', 0) and
            previous.get('macd', 0) <= previous.get('macd_signal', 0)
        )
        
        # MACD Bearish Cross (MACD cắt xuống dưới Signal)
        macd_bearish_cross = (
            current.get('macd', 0) < current.get('macd_signal', 0) and
            previous.get('macd', 0) >= previous.get('macd_signal', 0)
        )
        
        if macd_bullish_cross:
            return 'long'
        elif macd_bearish_cross:
            return 'short'
        
        return None
    
    def check_exit_signals(self, data: Dict[str, pd.DataFrame]) -> bool:
        """Kiểm tra tín hiệu thoát lệnh"""
        if self.open_trade is None:
            return False
        
        if 'ITF' not in data or data['ITF'].empty or len(data['ITF']) < 2:
            return False
        
        itf_data = data['ITF']
        current = itf_data.iloc[-1]
        previous = itf_data.iloc[-2]
        
        if self.open_trade.side == 'long':
            # Exit long khi MACD bearish cross
            exit_signal = (
                current.get('macd', 0) < current.get('macd_signal', 0) and
                previous.get('macd', 0) >= previous.get('macd_signal', 0)
            )
        else:  # short
            # Exit short khi MACD bullish cross
            exit_signal = (
                current.get('macd', 0) > current.get('macd_signal', 0) and
                previous.get('macd', 0) <= previous.get('macd_signal', 0)
            )
        
        return exit_signal

def test_basic_strategy():
    """Test chiến lược cơ bản"""
    from backtest import BacktestEngine
    
    print("🧪 TEST BASIC MACD STRATEGY")
    print("=" * 50)
    
    # Tạo engine với strategy cơ bản
    engine = BacktestEngine(initial_capital=5000)
    engine.strategy = BasicMacdStrategy(5000)
    
    # Chuẩn bị dữ liệu
    data = engine.prepare_data(config.SYMBOL, 90)
    if not data or 'LTF' not in data:
        print("❌ Không thể chuẩn bị dữ liệu")
        return
    
    ltf_data = data['LTF']
    total_bars = len(ltf_data)
    start_idx = 50
    
    print(f"📈 Test từ nến {start_idx} đến {total_bars}")
    
    signal_count = 0
    
    # Test signals trước
    for i in range(start_idx, min(start_idx + 200, total_bars)):
        current_data = {
            'HTF': data['HTF'].iloc[:i+1] if 'HTF' in data else pd.DataFrame(),
            'ITF': data['ITF'].iloc[:i+1] if 'ITF' in data else pd.DataFrame(), 
            'LTF': data['LTF'].iloc[:i+1]
        }
        
        # Reset trade để test signals
        engine.strategy.open_trade = None
        
        signal = engine.strategy.check_entry_signals(current_data)
        if signal:
            signal_count += 1
            timestamp = ltf_data.index[i]
            price = ltf_data.iloc[i]['close']
            
            # Lấy MACD values để debug
            if 'ITF' in current_data and not current_data['ITF'].empty:
                itf_latest = current_data['ITF'].iloc[-1]
                macd = itf_latest.get('macd', 0)
                macd_signal = itf_latest.get('macd_signal', 0)
                print(f"🎯 Signal {signal_count}: {signal.upper()} tại {timestamp}")
                print(f"   Giá: ${price:,.2f} | MACD: {macd:.2f} | Signal: {macd_signal:.2f}")
    
    print(f"\n📊 Tổng signals tìm thấy: {signal_count}")
    
    if signal_count > 0:
        print("✅ Strategy có thể tạo signals!")
        
        # Reset và chạy backtest thực
        engine.strategy = BasicMacdStrategy(5000)
        
        print("\n🚀 Chạy backtest với basic strategy...")
        
        for i in range(start_idx, total_bars):
            current_time = ltf_data.index[i]
            current_data = {
                'HTF': data['HTF'].iloc[:i+1] if 'HTF' in data else pd.DataFrame(),
                'ITF': data['ITF'].iloc[:i+1] if 'ITF' in data else pd.DataFrame(), 
                'LTF': data['LTF'].iloc[:i+1]
            }
            
            current_bar = ltf_data.iloc[i]
            
            # Kiểm tra stop loss / take profit
            engine.strategy.check_stop_loss_take_profit(
                current_time, 
                current_bar['high'], 
                current_bar['low']
            )
            
            # Kiểm tra exit
            if engine.strategy.open_trade is not None:
                if engine.strategy.check_exit_signals(current_data):
                    engine.strategy.close_position(
                        current_time, 
                        current_bar['close'], 
                        'exit_signal'
                    )
            
            # Kiểm tra entry
            if engine.strategy.open_trade is None:
                entry_signal = engine.strategy.check_entry_signals(current_data)
                if entry_signal:
                    engine.strategy.open_position(
                        current_time,
                        current_bar['close'],
                        entry_signal,
                        current_data['LTF']
                    )
        
        # Đóng lệnh cuối nếu còn mở
        if engine.strategy.open_trade is not None:
            final_price = ltf_data.iloc[-1]['close']
            final_time = ltf_data.index[-1]
            engine.strategy.close_position(final_time, final_price, 'backtest_end')
        
        # Kết quả
        results = engine.strategy.get_performance_metrics()
        
        print(f"\n📊 KẾT QUẢ BASIC STRATEGY:")
        print(f"Tổng giao dịch: {results.get('total_trades', 0)}")
        if results.get('total_trades', 0) > 0:
            print(f"Tỷ lệ thắng: {results.get('win_rate', 0)*100:.1f}%")
            print(f"Tổng return: {results.get('total_return', 0)*100:.1f}%")
            print(f"Profit factor: {results.get('profit_factor', 0):.2f}")
            print(f"Max drawdown: {results.get('max_drawdown', 0)*100:.1f}%")
            
            # In chi tiết trades
            print(f"\n📋 CHI TIẾT TRADES:")
            for i, trade in enumerate(engine.strategy.trades):
                duration = (trade.exit_time - trade.entry_time).total_seconds() / 3600 if trade.exit_time else 0
                print(f"Trade {i+1}: {trade.side.upper()} | "
                      f"Entry: ${trade.entry_price:.2f} | "
                      f"Exit: ${trade.exit_price:.2f} | "
                      f"PnL: ${trade.pnl:.2f} ({trade.pnl_pct*100:.1f}%) | "
                      f"Duration: {duration:.1f}h")
        else:
            print("❌ Không có giao dịch nào được thực hiện")
        
    else:
        print("❌ Không tìm thấy signals nào")
        
        # Debug MACD values
        print("\n🔍 DEBUG MACD VALUES:")
        if 'ITF' in data and not data['ITF'].empty:
            itf_data = data['ITF'].tail(10)
            for i, (timestamp, row) in enumerate(itf_data.iterrows()):
                macd = row.get('macd', 0)
                macd_signal = row.get('macd_signal', 0)
                cross_type = "BULL" if macd > macd_signal else "BEAR"
                print(f"{timestamp}: MACD={macd:.2f} | Signal={macd_signal:.2f} | {cross_type}")

if __name__ == "__main__":
    test_basic_strategy()
