{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Bitcoin MACD + RSI Multi-Timeframe Strategy Demo\n", "\n", "Notebook demo cho chiến lư<PERSON><PERSON> giao dịch Bitcoin sử dụng MACD + RSI với phân tích đa khung thời gian."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import c<PERSON><PERSON> thư viện c<PERSON>n thiết\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Import modules của project\n", "from data_fetcher import DataFetcher\n", "from indicators import TechnicalIndicators, MultiTimeframeAnalysis\n", "from strategy import MacdRsiStrategy\n", "from backtest import BacktestEngine\n", "from visualization import TradingVisualizer\n", "import config\n", "\n", "print(\"✅ Đã import thành công tất cả modules\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. <PERSON><PERSON><PERSON>u Bitcoin"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Khởi tạo data fetcher\n", "fetcher = DataFetcher()\n", "\n", "# <PERSON><PERSON><PERSON> dữ liệu cho các timeframe\n", "print(\"📊 Đang lấy dữ liệu Bitcoin...\")\n", "data = fetcher.fetch_multiple_timeframes(config.SYMBOL, config.TIMEFRAMES, 500)\n", "\n", "# <PERSON><PERSON><PERSON> thị thông tin dữ liệu\n", "for tf_name, df in data.items():\n", "    print(f\"{tf_name}: {len(df)} nến từ {df.index[0]} đến {df.index[-1]}\")\n", "    print(f\"   Gi<PERSON> cuối: ${df['close'].iloc[-1]:,.2f}\")\n", "    print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. <PERSON><PERSON><PERSON> Chỉ <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Tính toán indicators cho mỗi timeframe\n", "processed_data = {}\n", "\n", "for tf_name, df in data.items():\n", "    print(f\"🔧 Tính toán indicators cho {tf_name}...\")\n", "    df_with_indicators = TechnicalIndicators.calculate_all_indicators(df)\n", "    processed_data[tf_name] = df_with_indicators\n", "    \n", "    # <PERSON><PERSON><PERSON> thị indicators mới nhất\n", "    latest = df_with_indicators.iloc[-1]\n", "    print(f\"   MACD: {latest['macd']:.4f} | Signal: {latest['macd_signal']:.4f}\")\n", "    print(f\"   RSI: {latest['rsi']:.2f}\")\n", "    print()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. <PERSON><PERSON> Multi-Timeframe"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON> tích tín hiệu đa khung thời gian\n", "signals = MultiTimeframeAnalysis.get_mtf_signals(processed_data)\n", "\n", "print(\"🔍 PHÂN TÍCH ĐA KHUNG THỜI GIAN:\")\n", "print(f\"HTF Trend (Daily): {signals['htf_trend'].upper()}\")\n", "print(f\"ITF Setup (4H): {'✅' if signals['itf_setup'] else '❌'}\")\n", "print(f\"LTF Entry (1H): {'✅' if signals['ltf_entry'] else '❌'}\")\n", "print(f\"Signal Type: {signals['signal_type'].upper()}\")\n", "print(f\"Confidence: {signals['confidence']*100:.1f}%\")\n", "\n", "# <PERSON><PERSON><PERSON><PERSON>n nghị\n", "if signals['confidence'] >= 0.8:\n", "    if signals['signal_type'] == 'long':\n", "        print(\"\\n🟢 TÍN HIỆU LONG MẠNH\")\n", "    elif signals['signal_type'] == 'short':\n", "        print(\"\\n🔴 TÍN HIỆU SHORT MẠNH\")\n", "elif signals['confidence'] >= 0.6:\n", "    print(\"\\n🟡 TÍN HIỆU TRUNG BÌNH\")\n", "else:\n", "    print(\"\\n⚪ KHÔNG CÓ TÍN HIỆU RÕ RÀNG\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON> backtest với dữ liệu 6 tháng\n", "print(\"🚀 Bắt đầu backtest...\")\n", "\n", "engine = BacktestEngine(initial_capital=10000)\n", "results = engine.run_backtest(symbol=config.SYMBOL, lookback_days=180)\n", "\n", "if results:\n", "    engine.print_results(results)\n", "else:\n", "    print(\"❌ Backtest thất bại!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Tạo biểu đồ nếu có giao dịch\n", "if results and results.get('total_trades', 0) > 0:\n", "    print(\"📊 Tạo biểu đồ phân tích...\")\n", "    \n", "    # <PERSON><PERSON><PERSON> dữ liệu cho visualization\n", "    viz_data = engine.prepare_data(config.SYMBOL, 180)\n", "    trades = engine.strategy.trades\n", "    \n", "    if viz_data and trades:\n", "        viz = TradingVisualizer()\n", "        \n", "        # Tạo price chart với signals\n", "        price_fig = viz.plot_price_with_signals(viz_data, trades, 'LTF', save=False)\n", "        if price_fig:\n", "            price_fig.show()\n", "        \n", "        # Tạo equity curve\n", "        equity_fig = viz.plot_equity_curve(trades, 10000, save=False)\n", "        if equity_fig:\n", "            equity_fig.show()\n", "        \n", "        # Trade analysis\n", "        viz.plot_trade_analysis(trades, save=False)\n", "    else:\n", "        print(\"⚠️ Không thể tạo biểu đồ - thiếu dữ liệu\")\n", "else:\n", "    print(\"⚠️ Không có giao dịch để hiển thị\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON> thị chi tiết các giao dịch\n", "if results and engine.strategy.trades:\n", "    trades_df = []\n", "    \n", "    for i, trade in enumerate(engine.strategy.trades):\n", "        trades_df.append({\n", "            'Trade': i + 1,\n", "            'Side': trade.side.upper(),\n", "            'Entry Time': trade.entry_time.strftime('%Y-%m-%d %H:%M'),\n", "            'Entry Price': f\"${trade.entry_price:,.2f}\",\n", "            'Exit Time': trade.exit_time.strftime('%Y-%m-%d %H:%M') if trade.exit_time else 'N/A',\n", "            'Exit Price': f\"${trade.exit_price:,.2f}\" if trade.exit_price else 'N/A',\n", "            'PnL': f\"${trade.pnl:,.2f}\",\n", "            'PnL %': f\"{trade.pnl_pct*100:.2f}%\",\n", "            'Duration (h)': round((trade.exit_time - trade.entry_time).total_seconds() / 3600, 1) if trade.exit_time else 0\n", "        })\n", "    \n", "    trades_df = pd.DataFrame(trades_df)\n", "    print(\"📋 CHI TIẾT CÁC GIAO DỊCH:\")\n", "    print(trades_df.to_string(index=False))\n", "    \n", "    # <PERSON><PERSON><PERSON><PERSON> kê nhanh\n", "    winning_trades = len([t for t in engine.strategy.trades if t.pnl > 0])\n", "    total_trades = len(engine.strategy.trades)\n", "    \n", "    print(f\"\\n📊 THỐNG KÊ NHANH:\")\n", "    print(f\"Tổng giao dịch: {total_trades}\")\n", "    print(f\"<PERSON>ia<PERSON> dịch thắng: {winning_trades}\")\n", "    print(f\"Tỷ lệ thắng: {winning_trades/total_trades*100:.1f}%\")\n", "    print(f\"Lãi trung bình: ${sum(t.pnl for t in engine.strategy.trades)/total_trades:.2f}\")\n", "else:\n", "    print(\"⚠️ Không có giao dịch để phân tích\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. <PERSON> <PERSON><PERSON><PERSON>i Buy & Hold"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# So s<PERSON>h với chiến lược Buy & Hold\n", "if results and 'LTF' in processed_data:\n", "    ltf_data = processed_data['LTF']\n", "    \n", "    # Tính Buy & Hold return\n", "    start_price = ltf_data['close'].iloc[200]  # B<PERSON>t đầu từ nến 200\n", "    end_price = ltf_data['close'].iloc[-1]\n", "    buy_hold_return = (end_price / start_price - 1) * 100\n", "    \n", "    strategy_return = results.get('total_return', 0) * 100\n", "    \n", "    print(\"📈 SO SÁNH VỚI BUY & HOLD:\")\n", "    print(f\"Buy & Hold Return: {buy_hold_return:.2f}%\")\n", "    print(f\"Strategy Return: {strategy_return:.2f}%\")\n", "    print(f\"Outperformance: {strategy_return - buy_hold_return:.2f}%\")\n", "    \n", "    if strategy_return > buy_hold_return:\n", "        print(\"🎉 <PERSON><PERSON>n lư<PERSON> vượt trội hơn Buy & Hold!\")\n", "    else:\n", "        print(\"📉 Chiến lược kém hơn Buy & Hold\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON>\n", "\n", "Notebook này đã demo đầy đủ quy trình:\n", "\n", "1. ✅ <PERSON><PERSON><PERSON> dữ liệu Bitcoin từ exchange\n", "2. ✅ Tính toán chỉ báo MACD + RSI\n", "3. ✅ <PERSON><PERSON> tích đa khung thời gian\n", "4. ✅ <PERSON><PERSON><PERSON> backtest chiến l<PERSON>c\n", "5. ✅ Visualization kết quả\n", "6. ✅ So s<PERSON>h với Buy & Hold\n", "\n", "**Lưu ý**: <PERSON><PERSON><PERSON> chỉ là phân tích kỹ thuật cho mục đích nghiên cứu, không phải lời khuyên đầu tư!"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}