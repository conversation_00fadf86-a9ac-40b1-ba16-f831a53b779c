# 🐳 Bitcoin Trading Bot - Docker Setup

## 🚀 Quick Start

### 1. <PERSON><PERSON><PERSON> bị
```bash
# Clone hoặc copy project
cd /root/price-action-btc

# Tạo file .env từ template
cp .env.example .env

# Chỉnh sửa .env nếu cần (Telegram token, chat ID, etc.)
nano .env
```

### 2. <PERSON><PERSON><PERSON>
```bash
# Cấp quyền thực thi cho script quản lý
chmod +x docker-manager.sh

# Start bot
./docker-manager.sh start
```

## 📋 Các lệnh quản lý

### Khởi động bot
```bash
./docker-manager.sh start
```

### Dừng bot
```bash
./docker-manager.sh stop
```

### Restart bot
```bash
./docker-manager.sh restart
```

### Xem logs real-time
```bash
./docker-manager.sh logs
```

### Kiểm tra trạng thái
```bash
./docker-manager.sh status
```

### Build lại image
```bash
./docker-manager.sh build
```

## 📁 C<PERSON>u trú<PERSON> thư mục

```
/root/price-action-btc/
├── Dockerfile              # Docker image definition
├── docker-compose.yml      # Docker compose configuration
├── docker-manager.sh       # Management script
├── .env.example            # Environment template
├── .env                    # Your environment variables
├── logs/                   # Bot logs directory
├── results/                # Backtest results
├── trading_signals.log     # Trading signals log
└── ...                     # Python source files
```

## 🔧 Cấu hình

### Environment Variables (.env)
```bash
# Telegram
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id

# Trading
SYMBOL=BTC/USDT
CHECK_INTERVAL=300

# Timezone
TZ=Asia/Ho_Chi_Minh
```

### Docker Compose Features
- ✅ **Auto-restart**: Container tự động restart khi crash
- ✅ **Health check**: Kiểm tra kết nối Binance API
- ✅ **Resource limits**: Giới hạn CPU và RAM
- ✅ **Log rotation**: Tự động rotate logs
- ✅ **Volume mounts**: Lưu logs và data bên ngoài container

## 📊 Monitoring

### Xem logs
```bash
# Real-time logs
./docker-manager.sh logs

# Hoặc dùng docker trực tiếp
docker logs -f btc-trading-signals
```

### Kiểm tra resource usage
```bash
./docker-manager.sh status

# Hoặc
docker stats btc-trading-signals
```

### Kiểm tra health
```bash
docker inspect btc-trading-signals | grep -A 5 "Health"
```

## 🛠️ Troubleshooting

### Bot không start được
```bash
# Kiểm tra logs
./docker-manager.sh logs

# Rebuild image
./docker-manager.sh build
./docker-manager.sh start
```

### Không nhận được Telegram notifications
1. Kiểm tra bot token và chat ID trong `.env`
2. Test Telegram connection:
```bash
docker exec btc-trading-signals python3 test_telegram.py
```

### Container bị crash liên tục
```bash
# Xem logs để debug
./docker-manager.sh logs

# Kiểm tra resource usage
docker stats btc-trading-signals
```

## 🔄 Updates

### Update code và restart
```bash
# Pull latest code
git pull

# Rebuild và restart
./docker-manager.sh build
./docker-manager.sh restart
```

## 📈 Production Tips

1. **Monitoring**: Sử dụng `./docker-manager.sh status` để theo dõi
2. **Logs**: Logs được tự động rotate, không lo đầy disk
3. **Backup**: Backup thư mục `logs/` và `results/` định kỳ
4. **Security**: Container chạy với non-root user
5. **Resources**: Giới hạn 512MB RAM, 0.5 CPU cores

## 🆘 Support

Nếu gặp vấn đề:
1. Kiểm tra logs: `./docker-manager.sh logs`
2. Kiểm tra status: `./docker-manager.sh status`
3. Restart bot: `./docker-manager.sh restart`
