#!/bin/bash

# Bitcoin Trading Bot Docker Manager
# Usage: ./docker-manager.sh [start|stop|restart|logs|status|build]

set -e

BOT_NAME="btc-trading-signals"
COMPOSE_FILE="docker-compose.yml"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed!"
        exit 1
    fi

    # Check if docker compose plugin is available
    if ! docker compose version &> /dev/null; then
        print_error "Docker Compose plugin is not available!"
        print_error "Please install Docker with Compose plugin or use 'docker-compose' if available"
        exit 1
    fi
}

# Start the bot
start_bot() {
    print_status "Starting Bitcoin Trading Bot..."
    
    # Create necessary directories
    mkdir -p logs results
    
    # Copy .env file if it doesn't exist
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            print_warning "Created .env file from .env.example. Please review the settings."
        fi
    fi
    
    # Build and start
    docker-compose -f $COMPOSE_FILE up -d --build
    
    if [ $? -eq 0 ]; then
        print_success "Bot started successfully!"
        print_status "Container name: $BOT_NAME"
        print_status "Use './docker-manager.sh logs' to view logs"
        print_status "Use './docker-manager.sh status' to check status"
    else
        print_error "Failed to start bot!"
        exit 1
    fi
}

# Stop the bot
stop_bot() {
    print_status "Stopping Bitcoin Trading Bot..."
    docker-compose -f $COMPOSE_FILE down
    
    if [ $? -eq 0 ]; then
        print_success "Bot stopped successfully!"
    else
        print_error "Failed to stop bot!"
        exit 1
    fi
}

# Restart the bot
restart_bot() {
    print_status "Restarting Bitcoin Trading Bot..."
    stop_bot
    sleep 2
    start_bot
}

# Show logs
show_logs() {
    print_status "Showing bot logs (Ctrl+C to exit)..."
    docker-compose -f $COMPOSE_FILE logs -f bitcoin-trading-bot
}

# Show status
show_status() {
    print_status "Bitcoin Trading Bot Status:"
    echo ""
    
    # Container status
    if docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -q $BOT_NAME; then
        print_success "Container is running:"
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.CreatedAt}}" | grep $BOT_NAME
    else
        print_warning "Container is not running"
    fi
    
    echo ""
    
    # Resource usage
    if docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | grep -q $BOT_NAME; then
        print_status "Resource usage:"
        docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | grep $BOT_NAME
    fi
    
    echo ""
    
    # Recent logs
    print_status "Recent logs (last 10 lines):"
    docker-compose -f $COMPOSE_FILE logs --tail=10 bitcoin-trading-bot
}

# Build image
build_image() {
    print_status "Building Bitcoin Trading Bot image..."
    docker-compose -f $COMPOSE_FILE build --no-cache
    
    if [ $? -eq 0 ]; then
        print_success "Image built successfully!"
    else
        print_error "Failed to build image!"
        exit 1
    fi
}

# Main script
main() {
    check_docker
    
    case "${1:-}" in
        start)
            start_bot
            ;;
        stop)
            stop_bot
            ;;
        restart)
            restart_bot
            ;;
        logs)
            show_logs
            ;;
        status)
            show_status
            ;;
        build)
            build_image
            ;;
        *)
            echo "Bitcoin Trading Bot Docker Manager"
            echo ""
            echo "Usage: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  start    - Start the trading bot"
            echo "  stop     - Stop the trading bot"
            echo "  restart  - Restart the trading bot"
            echo "  logs     - Show bot logs (real-time)"
            echo "  status   - Show bot status and resource usage"
            echo "  build    - Build/rebuild the Docker image"
            echo ""
            echo "Examples:"
            echo "  $0 start     # Start the bot"
            echo "  $0 logs      # View logs"
            echo "  $0 status    # Check status"
            ;;
    esac
}

# Run main function
main "$@"
