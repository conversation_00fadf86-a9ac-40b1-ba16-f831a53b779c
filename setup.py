"""
Setup script cho Bitcoin Price Action Analysis
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Chạy command và hiển thị kết quả"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} thành công!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} thất bại: {e}")
        print(f"Error output: {e.stderr}")
        return False

def setup_environment():
    """Setup môi trường Python"""
    print("🚀 SETUP BITCOIN PRICE ACTION ANALYSIS")
    print("=" * 50)
    
    # Kiểm tra Python version
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ Cần Python 3.8 trở lên!")
        return False
    
    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Xác định python command
    python_cmd = "python3" if os.name != 'nt' else "python"

    # Tạo virtual environment nếu chưa có
    venv_path = Path("venv")
    if not venv_path.exists():
        if not run_command(f"{python_cmd} -m venv venv", "Tạo virtual environment"):
            return False
    else:
        print("✅ Virtual environment đã tồn tại")

    # Xác định activation script
    if os.name == 'nt':  # Windows
        activate_script = "venv\\Scripts\\activate"
        pip_command = "venv\\Scripts\\pip"
    else:  # Unix/Linux/Mac
        activate_script = "source venv/bin/activate"
        pip_command = "venv/bin/pip"
    
    # Upgrade pip
    if not run_command(f"{pip_command} install --upgrade pip", "Upgrade pip"):
        return False
    
    # Cài đặt requirements
    if not run_command(f"{pip_command} install -r requirements.txt", "Cài đặt dependencies"):
        return False
    
    # Tạo thư mục cần thiết
    directories = ['plots', 'results', 'data']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"📁 Tạo thư mục: {directory}")
    
    print("\n🎉 SETUP HOÀN THÀNH!")
    print("=" * 50)
    print("📋 HƯỚNG DẪN SỬ DỤNG:")
    print(f"1. Kích hoạt virtual environment:")
    if os.name == 'nt':
        print("   venv\\Scripts\\activate")
    else:
        print("   source venv/bin/activate")
    
    print("\n2. Chạy backtest:")
    print("   python main.py")
    print("   python main.py --days 180 --capital 5000")
    
    print("\n3. Phân tích real-time:")
    print("   python main.py --live-analysis")
    
    print("\n4. Xem thêm options:")
    print("   python main.py --help")
    
    return True

def test_installation():
    """Test cài đặt"""
    print("\n🧪 KIỂM TRA CÀI ĐẶT...")
    
    try:
        # Test import các module chính
        import ccxt
        import pandas as pd
        import numpy as np
        import ta
        import matplotlib.pyplot as plt
        import plotly.graph_objects as go
        
        print("✅ Tất cả dependencies đã được cài đặt")
        
        # Test kết nối exchange
        exchange = ccxt.binance()
        ticker = exchange.fetch_ticker('BTC/USDT')
        print(f"✅ Kết nối exchange thành công - BTC: ${ticker['last']:,.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi kiểm tra: {e}")
        return False

if __name__ == "__main__":
    if setup_environment():
        test_installation()
    else:
        print("❌ Setup thất bại!")
        sys.exit(1)
