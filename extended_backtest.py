"""
Extended Backtest từ 1/1/2025 đến 9/6/2025
"""

import pandas as pd
from datetime import datetime, timedelta
from backtest import BacktestEngine
from basic_strategy import BasicMacdStrategy
import config

def calculate_days_from_start():
    """T<PERSON>h số ngày từ 1/1/2025 đến 9/6/2025"""
    start_date = datetime(2025, 1, 1)
    end_date = datetime(2025, 6, 9)
    days = (end_date - start_date).days
    return days

def run_extended_backtest():
    """Chạy backtest mở rộng"""
    
    # Tính số ngày
    total_days = calculate_days_from_start()
    print(f"🗓️ EXTENDED BACKTEST: 1/1/2025 → 9/6/2025")
    print(f"📅 Tổng số ngày: {total_days} ngày")
    print("=" * 60)
    
    # Khởi tạo engine
    initial_capital = 10000
    engine = BacktestEngine(initial_capital=initial_capital)
    engine.strategy = BasicMacdStrategy(initial_capital)
    
    # Chạy backtest với số ngày mở rộng
    print(f"🚀 Bắt đầu backtest {total_days} ngày...")
    results = engine.run_backtest(
        symbol=config.SYMBOL,
        lookback_days=total_days
    )
    
    if not results:
        print("❌ Backtest thất bại!")
        return
    
    # In kết quả chi tiết
    print_extended_results(results, total_days)
    
    # Phân tích theo tháng
    analyze_monthly_performance(engine.strategy.trades)
    
    return results

def print_extended_results(results: dict, total_days: int):
    """In kết quả backtest mở rộng"""
    
    print("\n" + "="*70)
    print("📊 KẾT QUẢ EXTENDED BACKTEST (1/1/2025 - 9/6/2025)")
    print("="*70)
    
    # Thông tin cơ bản
    print(f"🎯 Symbol: {results.get('symbol', 'N/A')}")
    print(f"📅 Thời gian: {results.get('start_date', 'N/A')} → {results.get('end_date', 'N/A')}")
    print(f"📊 Tổng số ngày: {total_days}")
    print(f"💰 Vốn ban đầu: ${results.get('final_capital', 0) - results.get('total_pnl', 0):,.2f}")
    print(f"💰 Vốn cuối: ${results.get('final_capital', 0):,.2f}")
    
    # Hiệu suất
    total_return = results.get('total_return', 0)
    monthly_return = (total_return / (total_days / 30.44)) if total_days > 0 else 0
    annualized_return = (total_return / (total_days / 365.25)) if total_days > 0 else 0
    
    print(f"\n📈 HIỆU SUẤT:")
    print(f"   • Tổng lợi nhuận: ${results.get('total_pnl', 0):,.2f}")
    print(f"   • Tỷ suất sinh lời: {total_return*100:.2f}%")
    print(f"   • Return/tháng: {monthly_return*100:.2f}%")
    print(f"   • Annualized return: {annualized_return*100:.2f}%")
    print(f"   • Drawdown tối đa: {results.get('max_drawdown', 0)*100:.2f}%")
    
    # Giao dịch
    total_trades = results.get('total_trades', 0)
    trades_per_month = (total_trades / (total_days / 30.44)) if total_days > 0 else 0
    
    print(f"\n🎲 GIAO DỊCH:")
    print(f"   • Tổng số giao dịch: {total_trades}")
    print(f"   • Giao dịch/tháng: {trades_per_month:.1f}")
    print(f"   • Giao dịch thắng: {results.get('winning_trades', 0)}")
    print(f"   • Giao dịch thua: {results.get('losing_trades', 0)}")
    print(f"   • Tỷ lệ thắng: {results.get('win_rate', 0)*100:.2f}%")
    
    # Chỉ số
    print(f"\n💡 CHỈ SỐ:")
    print(f"   • Profit Factor: {results.get('profit_factor', 0):.2f}")
    print(f"   • Lãi TB/giao dịch: ${results.get('avg_trade', 0):.2f}")
    print(f"   • Lãi TB khi thắng: ${results.get('avg_win', 0):.2f}")
    print(f"   • Lỗ TB khi thua: ${results.get('avg_loss', 0):.2f}")
    
    # Đánh giá
    score = calculate_strategy_score(results, annualized_return)
    rating = get_strategy_rating(score)
    print(f"\n🎯 ĐÁNH GIÁ:")
    print(f"   Điểm số: {score}/100 - {rating}")
    
    # So sánh với Buy & Hold (ước tính)
    btc_start_2025 = 42000  # Ước tính giá BTC đầu năm 2025
    btc_current = 107000    # Giá hiện tại
    buy_hold_return = (btc_current / btc_start_2025 - 1) * 100
    
    print(f"\n📊 SO SÁNH:")
    print(f"   • Strategy return: {total_return*100:.2f}%")
    print(f"   • Buy & Hold (ước tính): {buy_hold_return:.2f}%")
    print(f"   • Outperformance: {total_return*100 - buy_hold_return:.2f}%")
    
    print("="*70)

def analyze_monthly_performance(trades):
    """Phân tích hiệu suất theo tháng"""
    if not trades:
        return
    
    print(f"\n📅 PHÂN TÍCH THEO THÁNG:")
    print("-" * 50)
    
    # Nhóm trades theo tháng
    monthly_pnl = {}
    monthly_trades = {}
    
    for trade in trades:
        if trade.exit_time:
            month_key = trade.exit_time.strftime('%Y-%m')
            if month_key not in monthly_pnl:
                monthly_pnl[month_key] = 0
                monthly_trades[month_key] = 0
            monthly_pnl[month_key] += trade.pnl
            monthly_trades[month_key] += 1
    
    # In kết quả từng tháng
    total_monthly_pnl = 0
    for month in sorted(monthly_pnl.keys()):
        pnl = monthly_pnl[month]
        trades_count = monthly_trades[month]
        total_monthly_pnl += pnl
        
        status = "🟢" if pnl > 0 else "🔴" if pnl < 0 else "⚪"
        print(f"{month}: {status} ${pnl:>8.2f} ({trades_count:>2} trades) | Cumulative: ${total_monthly_pnl:>8.2f}")

def calculate_strategy_score(results: dict, annualized_return: float) -> int:
    """Tính điểm strategy"""
    score = 0
    
    # Win rate (25 điểm)
    win_rate = results.get('win_rate', 0)
    if win_rate >= 0.6:
        score += 25
    elif win_rate >= 0.5:
        score += 20
    elif win_rate >= 0.4:
        score += 10
    
    # Profit factor (25 điểm)
    profit_factor = results.get('profit_factor', 0)
    if profit_factor >= 3.0:
        score += 25
    elif profit_factor >= 2.0:
        score += 20
    elif profit_factor >= 1.5:
        score += 15
    elif profit_factor >= 1.0:
        score += 10
    
    # Annualized return (25 điểm)
    if annualized_return >= 0.5:  # 50%+
        score += 25
    elif annualized_return >= 0.3:  # 30%+
        score += 20
    elif annualized_return >= 0.2:  # 20%+
        score += 15
    elif annualized_return >= 0.1:  # 10%+
        score += 10
    
    # Max drawdown (25 điểm)
    max_dd = results.get('max_drawdown', 1.0)
    if max_dd <= 0.05:  # 5%
        score += 25
    elif max_dd <= 0.10:  # 10%
        score += 20
    elif max_dd <= 0.15:  # 15%
        score += 15
    elif max_dd <= 0.25:  # 25%
        score += 10
    
    return min(score, 100)

def get_strategy_rating(score: int) -> str:
    """Đánh giá strategy dựa trên điểm"""
    if score >= 90:
        return "🌟 XUẤT SẮC"
    elif score >= 75:
        return "🏆 RẤT TỐT"
    elif score >= 60:
        return "✅ TỐT"
    elif score >= 40:
        return "⚠️ TRUNG BÌNH"
    else:
        return "❌ YẾU"

if __name__ == "__main__":
    try:
        results = run_extended_backtest()
        
        if results and results.get('total_trades', 0) > 0:
            print(f"\n💾 Kết quả đã được lưu vào thư mục results/")
            print(f"🎯 Tóm tắt: {results.get('total_trades', 0)} giao dịch, "
                  f"{results.get('total_return', 0)*100:.1f}% return, "
                  f"{results.get('max_drawdown', 0)*100:.1f}% max drawdown")
        else:
            print("\n⚠️ Không có giao dịch nào được thực hiện trong khoảng thời gian này")
            
    except Exception as e:
        print(f"❌ Lỗi trong quá trình backtest: {e}")
        import traceback
        traceback.print_exc()
