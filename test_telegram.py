#!/usr/bin/env python3
"""
Test Telegram notification
"""

import requests
from datetime import datetime

def test_telegram():
    """Test gửi thông báo Telegram"""
    
    # Telegram config
    bot_token = "**********************************************"
    chat_id = "-4747894787"
    
    # Test message
    message = f"""
🧪 <b>TEST TELEGRAM NOTIFICATION</b> 🧪

✅ <b>Status:</b> Connection successful!
⏰ <b>Time:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🤖 <b>Bot:</b> Bitcoin Trading Signals

📱 Telegram integration is working properly!
    """.strip()
    
    try:
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        data = {
            'chat_id': chat_id,
            'text': message,
            'parse_mode': 'HTML'
        }
        
        print("📱 Đang gửi test message...")
        response = requests.post(url, data=data, timeout=10)
        
        if response.status_code == 200:
            print("✅ Test thành công! Kiểm tra Telegram của bạn.")
            print(f"📊 Response: {response.json()}")
        else:
            print(f"❌ Lỗi: {response.status_code}")
            print(f"📄 Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_telegram()
