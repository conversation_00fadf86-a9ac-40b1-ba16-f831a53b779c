"""
Long-term backtest với dữ liệu từ 1/1/2025 đến 9/6/2025
L<PERSON>y dữ liệu theo batch để vượt qua giới hạn 1000 nến
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import ccxt
from basic_strategy import BasicMacdStrategy
from indicators import TechnicalIndicators
import config

class LongTermDataFetcher:
    """Lấy dữ liệu dài hạn theo batch"""
    
    def __init__(self):
        self.exchange = ccxt.binance({'enableRateLimit': True})
    
    def fetch_historical_data(self, symbol: str, timeframe: str, start_date: datetime, end_date: datetime):
        """Lấy dữ liệu lịch sử từ start_date đến end_date"""
        
        print(f"📊 Lấy dữ liệu {timeframe} từ {start_date.date()} đến {end_date.date()}...")
        
        all_data = []
        current_time = start_date
        
        # Tính milliseconds
        timeframe_ms = {
            '1h': 60 * 60 * 1000,
            '4h': 4 * 60 * 60 * 1000,
            '1d': 24 * 60 * 60 * 1000
        }
        
        ms_per_candle = timeframe_ms.get(timeframe, 60 * 60 * 1000)
        
        while current_time < end_date:
            try:
                # Lấy 1000 nến từ current_time
                since = int(current_time.timestamp() * 1000)
                ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe, limit=1000, since=since)
                
                if not ohlcv:
                    break
                
                # Convert to DataFrame
                df_batch = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                df_batch['timestamp'] = pd.to_datetime(df_batch['timestamp'], unit='ms')
                
                # Filter trong khoảng thời gian
                df_batch = df_batch[
                    (df_batch['timestamp'] >= start_date) & 
                    (df_batch['timestamp'] <= end_date)
                ]
                
                if not df_batch.empty:
                    all_data.append(df_batch)
                    print(f"   ✅ Lấy được {len(df_batch)} nến từ {df_batch['timestamp'].iloc[0].date()}")
                
                # Update current_time cho batch tiếp theo
                if len(ohlcv) == 1000:
                    current_time = pd.to_datetime(ohlcv[-1][0], unit='ms') + timedelta(milliseconds=ms_per_candle)
                else:
                    break
                
                # Rate limiting
                time.sleep(0.5)
                
            except Exception as e:
                print(f"❌ Lỗi lấy dữ liệu: {e}")
                time.sleep(2)
                continue
        
        if all_data:
            # Combine tất cả data
            combined_df = pd.concat(all_data, ignore_index=True)
            combined_df = combined_df.drop_duplicates(subset=['timestamp']).sort_values('timestamp')
            combined_df.set_index('timestamp', inplace=True)
            
            # Ensure numeric types
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            combined_df[numeric_columns] = combined_df[numeric_columns].astype(float)
            
            print(f"✅ Tổng cộng: {len(combined_df)} nến {timeframe}")
            return combined_df
        else:
            print(f"❌ Không lấy được dữ liệu nào")
            return pd.DataFrame()

def run_long_term_backtest():
    """Chạy backtest dài hạn"""
    
    print("🗓️ LONG-TERM BACKTEST: 1/1/2025 → 9/6/2025")
    print("=" * 60)
    
    # Định nghĩa thời gian
    start_date = datetime(2025, 1, 1)
    end_date = datetime(2025, 6, 9)
    total_days = (end_date - start_date).days
    
    print(f"📅 Thời gian: {start_date.date()} → {end_date.date()}")
    print(f"📊 Tổng số ngày: {total_days}")
    
    # Lấy dữ liệu
    fetcher = LongTermDataFetcher()
    
    # Lấy dữ liệu 1H (chính)
    data_1h = fetcher.fetch_historical_data(config.SYMBOL, '1h', start_date, end_date)
    if data_1h.empty:
        print("❌ Không thể lấy dữ liệu 1H")
        return
    
    # Lấy dữ liệu 4H cho signals
    data_4h = fetcher.fetch_historical_data(config.SYMBOL, '4h', start_date, end_date)
    if data_4h.empty:
        print("❌ Không thể lấy dữ liệu 4H")
        return
    
    print(f"\n📊 Dữ liệu đã sẵn sàng:")
    print(f"   • 1H: {len(data_1h)} nến ({data_1h.index[0].date()} → {data_1h.index[-1].date()})")
    print(f"   • 4H: {len(data_4h)} nến ({data_4h.index[0].date()} → {data_4h.index[-1].date()})")
    
    # Tính toán indicators
    print(f"\n🔧 Tính toán indicators...")
    data_1h_with_indicators = TechnicalIndicators.calculate_all_indicators(data_1h)
    data_4h_with_indicators = TechnicalIndicators.calculate_all_indicators(data_4h)
    
    # Chạy backtest
    print(f"\n🚀 Bắt đầu backtest...")
    
    initial_capital = 10000
    strategy = BasicMacdStrategy(initial_capital)
    
    # Bỏ qua 200 nến đầu để indicators ổn định
    start_idx = 200
    if len(data_1h_with_indicators) <= start_idx:
        print(f"❌ Không đủ dữ liệu. Cần ít nhất {start_idx} nến")
        return
    
    print(f"📈 Backtest từ nến {start_idx} đến {len(data_1h_with_indicators)}")
    
    # Backtest loop
    for i in range(start_idx, len(data_1h_with_indicators)):
        current_time = data_1h_with_indicators.index[i]
        current_bar = data_1h_with_indicators.iloc[i]
        
        # Tạo dữ liệu cho strategy (chỉ đến thời điểm hiện tại)
        current_data = {
            'ITF': data_4h_with_indicators[data_4h_with_indicators.index <= current_time],
            'LTF': data_1h_with_indicators.iloc[:i+1]
        }
        
        # Kiểm tra stop loss / take profit
        strategy.check_stop_loss_take_profit(
            current_time, 
            current_bar['high'], 
            current_bar['low']
        )
        
        # Kiểm tra exit signals
        if strategy.open_trade is not None:
            if strategy.check_exit_signals(current_data):
                strategy.close_position(
                    current_time, 
                    current_bar['close'], 
                    'exit_signal'
                )
        
        # Kiểm tra entry signals
        if strategy.open_trade is None:
            entry_signal = strategy.check_entry_signals(current_data)
            if entry_signal:
                strategy.open_position(
                    current_time,
                    current_bar['close'],
                    entry_signal,
                    current_data['LTF']
                )
        
        # Progress update
        if i % 1000 == 0:
            progress = (i - start_idx) / (len(data_1h_with_indicators) - start_idx) * 100
            print(f"⏳ Progress: {progress:.1f}% | Trades: {len(strategy.trades)} | Capital: ${strategy.current_capital:.2f}")
    
    # Đóng lệnh cuối nếu còn mở
    if strategy.open_trade is not None:
        final_price = data_1h_with_indicators.iloc[-1]['close']
        final_time = data_1h_with_indicators.index[-1]
        strategy.close_position(final_time, final_price, 'backtest_end')
    
    # Tính toán kết quả
    results = strategy.get_performance_metrics()
    results['start_date'] = data_1h_with_indicators.index[start_idx]
    results['end_date'] = data_1h_with_indicators.index[-1]
    results['total_days'] = total_days
    results['actual_trading_days'] = (results['end_date'] - results['start_date']).days
    
    # In kết quả
    print_long_term_results(results, strategy.trades)
    
    return results

def print_long_term_results(results: dict, trades):
    """In kết quả backtest dài hạn"""
    
    print("\n" + "="*70)
    print("📊 KẾT QUẢ LONG-TERM BACKTEST")
    print("="*70)
    
    actual_days = results.get('actual_trading_days', 0)
    total_return = results.get('total_return', 0)
    
    print(f"🎯 Symbol: BTC/USDT")
    print(f"📅 Thời gian: {results.get('start_date', 'N/A')} → {results.get('end_date', 'N/A')}")
    print(f"📊 Số ngày giao dịch: {actual_days}")
    print(f"💰 Vốn ban đầu: ${results.get('final_capital', 0) - results.get('total_pnl', 0):,.2f}")
    print(f"💰 Vốn cuối: ${results.get('final_capital', 0):,.2f}")
    
    # Tính toán returns
    if actual_days > 0:
        monthly_return = total_return * (30.44 / actual_days)
        annualized_return = total_return * (365.25 / actual_days)
    else:
        monthly_return = 0
        annualized_return = 0
    
    print(f"\n📈 HIỆU SUẤT:")
    print(f"   • Tổng lợi nhuận: ${results.get('total_pnl', 0):,.2f}")
    print(f"   • Tỷ suất sinh lời: {total_return*100:.2f}%")
    print(f"   • Return/tháng: {monthly_return*100:.2f}%")
    print(f"   • Annualized return: {annualized_return*100:.2f}%")
    print(f"   • Drawdown tối đa: {results.get('max_drawdown', 0)*100:.2f}%")
    
    print(f"\n🎲 GIAO DỊCH:")
    print(f"   • Tổng số giao dịch: {results.get('total_trades', 0)}")
    print(f"   • Tỷ lệ thắng: {results.get('win_rate', 0)*100:.2f}%")
    print(f"   • Profit Factor: {results.get('profit_factor', 0):.2f}")
    
    # Phân tích theo tháng
    if trades:
        print(f"\n📅 PHÂN TÍCH THEO THÁNG:")
        monthly_pnl = {}
        for trade in trades:
            if trade.exit_time:
                month_key = trade.exit_time.strftime('%Y-%m')
                monthly_pnl[month_key] = monthly_pnl.get(month_key, 0) + trade.pnl
        
        cumulative = 0
        for month in sorted(monthly_pnl.keys()):
            pnl = monthly_pnl[month]
            cumulative += pnl
            status = "🟢" if pnl > 0 else "🔴" if pnl < 0 else "⚪"
            print(f"   {month}: {status} ${pnl:>8.2f} | Cumulative: ${cumulative:>8.2f}")
    
    print("="*70)

if __name__ == "__main__":
    try:
        results = run_long_term_backtest()
        if results:
            print(f"\n🎯 Tóm tắt: {results.get('total_trades', 0)} giao dịch, "
                  f"{results.get('total_return', 0)*100:.1f}% return")
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()
