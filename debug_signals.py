"""
Debug script để kiểm tra tại sao không có tín hiệu giao dịch
"""

import pandas as pd
from data_fetcher import DataFetcher
from indicators import TechnicalIndicators, MultiTimeframeAnalysis
import config

def debug_signals():
    """Debug tín hiệu giao dịch"""
    print("🔍 DEBUG SIGNALS")
    print("=" * 50)
    
    # Lấy dữ liệu
    fetcher = DataFetcher()
    data = fetcher.fetch_multiple_timeframes(config.SYMBOL, config.TIMEFRAMES, 200)
    
    if not data:
        print("❌ Không thể lấy dữ liệu")
        return
    
    # Tính toán indicators
    processed_data = {}
    for tf_name, df in data.items():
        processed_data[tf_name] = TechnicalIndicators.calculate_all_indicators(df)
    
    # Kiểm tra 10 nến cuối
    print("\n📊 PHÂN TÍCH 10 NẾN CUỐI:")
    
    for i in range(-10, 0):
        print(f"\n--- Nến {i} ---")
        
        # Tạo dữ liệu cho nến này
        current_data = {}
        for tf_name, df in processed_data.items():
            if len(df) > abs(i):
                current_data[tf_name] = df.iloc[:i] if i < -1 else df
            else:
                current_data[tf_name] = df
        
        # Phân tích signals
        signals = MultiTimeframeAnalysis.get_mtf_signals(current_data)
        
        print(f"HTF Trend: {signals['htf_trend']}")
        print(f"ITF Setup: {signals['itf_setup']}")
        print(f"LTF Entry: {signals['ltf_entry']}")
        print(f"Signal Type: {signals['signal_type']}")
        print(f"Confidence: {signals['confidence']:.2f}")
        
        # Chi tiết ITF
        if 'ITF' in current_data and not current_data['ITF'].empty:
            itf_latest = current_data['ITF'].iloc[-1]
            print(f"ITF Details:")
            print(f"  RSI: {itf_latest.get('rsi', 0):.2f}")
            print(f"  RSI from oversold: {itf_latest.get('rsi_from_oversold', False)}")
            print(f"  RSI from overbought: {itf_latest.get('rsi_from_overbought', False)}")
            print(f"  MACD bullish cross: {itf_latest.get('macd_bullish_cross', False)}")
            print(f"  MACD bearish cross: {itf_latest.get('macd_bearish_cross', False)}")
        
        # Chi tiết LTF
        if 'LTF' in current_data and not current_data['LTF'].empty:
            ltf_latest = current_data['LTF'].iloc[-1]
            print(f"LTF Details:")
            print(f"  MACD: {ltf_latest.get('macd', 0):.4f}")
            print(f"  MACD Signal: {ltf_latest.get('macd_signal', 0):.4f}")
            print(f"  Volume above avg: {ltf_latest.get('volume_above_avg', False)}")

def check_individual_conditions():
    """Kiểm tra từng điều kiện riêng lẻ"""
    print("\n🔍 KIỂM TRA ĐIỀU KIỆN RIÊNG LẺ:")
    print("=" * 50)
    
    # Lấy dữ liệu
    fetcher = DataFetcher()
    data = fetcher.fetch_multiple_timeframes(config.SYMBOL, config.TIMEFRAMES, 500)
    
    # Tính toán indicators
    processed_data = {}
    for tf_name, df in data.items():
        processed_data[tf_name] = TechnicalIndicators.calculate_all_indicators(df)
    
    # Kiểm tra từng timeframe
    for tf_name, df in processed_data.items():
        print(f"\n📊 {tf_name} Analysis:")
        
        # Kiểm tra MACD crossovers trong 50 nến cuối
        macd_bull_crosses = df['macd_bullish_cross'].tail(50).sum()
        macd_bear_crosses = df['macd_bearish_cross'].tail(50).sum()
        
        print(f"  MACD Bullish crosses (last 50): {macd_bull_crosses}")
        print(f"  MACD Bearish crosses (last 50): {macd_bear_crosses}")
        
        # Kiểm tra RSI conditions
        rsi_from_oversold = df['rsi_from_oversold'].tail(50).sum()
        rsi_from_overbought = df['rsi_from_overbought'].tail(50).sum()
        
        print(f"  RSI from oversold (last 50): {rsi_from_oversold}")
        print(f"  RSI from overbought (last 50): {rsi_from_overbought}")
        
        # Kiểm tra volume
        if 'volume_above_avg' in df.columns:
            volume_above_avg = df['volume_above_avg'].tail(50).sum()
            print(f"  Volume above average (last 50): {volume_above_avg}")
        
        # Latest values
        latest = df.iloc[-1]
        print(f"  Latest RSI: {latest.get('rsi', 0):.2f}")
        print(f"  Latest MACD: {latest.get('macd', 0):.4f}")
        print(f"  Latest MACD Signal: {latest.get('macd_signal', 0):.4f}")

def test_relaxed_conditions():
    """Test với điều kiện nới lỏng"""
    print("\n🔍 TEST VỚI ĐIỀU KIỆN NỚI LỎNG:")
    print("=" * 50)
    
    # Lấy dữ liệu
    fetcher = DataFetcher()
    data = fetcher.fetch_multiple_timeframes(config.SYMBOL, config.TIMEFRAMES, 200)
    
    # Tính toán indicators
    processed_data = {}
    for tf_name, df in data.items():
        processed_data[tf_name] = TechnicalIndicators.calculate_all_indicators(df)
    
    # Đếm potential signals với điều kiện nới lỏng
    potential_long_signals = 0
    potential_short_signals = 0
    
    for i in range(50, len(processed_data['LTF'])):
        current_data = {}
        for tf_name, df in processed_data.items():
            current_data[tf_name] = df.iloc[:i+1]
        
        if 'ITF' in current_data and not current_data['ITF'].empty:
            itf_latest = current_data['ITF'].iloc[-1]
            
            # Relaxed long conditions
            relaxed_long = (
                itf_latest.get('rsi', 50) < 50 and  # RSI dưới 50 thay vì from oversold
                itf_latest.get('macd', 0) > itf_latest.get('macd_signal', 0)  # MACD > Signal
            )
            
            # Relaxed short conditions
            relaxed_short = (
                itf_latest.get('rsi', 50) > 50 and  # RSI trên 50 thay vì from overbought
                itf_latest.get('macd', 0) < itf_latest.get('macd_signal', 0)  # MACD < Signal
            )
            
            if relaxed_long:
                potential_long_signals += 1
            if relaxed_short:
                potential_short_signals += 1
    
    print(f"Potential LONG signals (relaxed): {potential_long_signals}")
    print(f"Potential SHORT signals (relaxed): {potential_short_signals}")
    
    # Test với confidence thấp hơn
    signals = MultiTimeframeAnalysis.get_mtf_signals(processed_data)
    print(f"\nCurrent confidence: {signals['confidence']:.2f}")
    print(f"Current signal type: {signals['signal_type']}")
    
    if signals['confidence'] > 0.3:  # Threshold rất thấp
        print("✅ Có thể có signal với threshold thấp hơn!")
    else:
        print("❌ Vẫn không có signal ngay cả với threshold thấp")

if __name__ == "__main__":
    debug_signals()
    check_individual_conditions()
    test_relaxed_conditions()
