# Bitcoin Price Action Analysis

Phân tích hiệu quả chiến lược MACD + RSI kết hợp đa khung thời gian cho giao dịch Bitcoin.

## Tính năng

- ✅ Thu thập dữ liệu BTC real-time từ nhiều sàn giao dịch (ccxt)
- ✅ Phân tích đa khung thời gian (Daily, 4H, 1H)
- ✅ Chiến lược MACD + RSI với xác nhận trend
- ✅ Backtest với metrics chi tiết
- ✅ Báo cáo hiệu suất và visualization

## Cài đặt

```bash
# Tạo virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# hoặc
venv\Scripts\activate     # Windows

# Cài đặt dependencies
pip install -r requirements.txt
```

## Sử dụng

```bash
python main.py
```

## Chiến lược

### MACD + RSI Multi-Timeframe
- **HTF (Daily)**: <PERSON><PERSON><PERSON> định xu hướng tổng thể
- **ITF (4H)**: T<PERSON><PERSON> kiếm setup entry
- **LTF (1H)**: Timing entry chính xác

### Điều kiện Long
1. Daily trend: MACD > Signal line
2. 4H: RSI từ oversold (<30) và MACD bullish cross
3. 1H: Xác nhận entry với volume

### Điều kiện Short
1. Daily trend: MACD < Signal line
2. 4H: RSI từ overbought (>70) và MACD bearish cross
3. 1H: Xác nhận entry với volume

## Cấu trúc Project

```
price-action-btc/
├── config.py              # Cấu hình chiến lược
├── data_fetcher.py         # Thu thập dữ liệu từ exchange
├── indicators.py           # Tính toán chỉ báo kỹ thuật
├── strategy.py             # Logic chiến lược giao dịch
├── backtest.py             # Engine backtest
├── visualization.py        # Tạo biểu đồ phân tích
├── main.py                 # Script chính
├── setup.py                # Script cài đặt
├── demo.ipynb              # Jupyter notebook demo
├── requirements.txt        # Dependencies
└── README.md
```

## Quick Start

### 1. Setup môi trường
```bash
# Clone repository
git clone <repo-url>
cd price-action-btc

# Chạy setup tự động
python setup.py
```

### 2. Kích hoạt virtual environment
```bash
# Linux/Mac
source venv/bin/activate

# Windows
venv\Scripts\activate
```

### 3. Chạy backtest
```bash
# Backtest cơ bản (365 ngày, $10,000)
python main.py

# Backtest tùy chỉnh
python main.py --days 180 --capital 5000 --symbol BTC/USDT

# Phân tích real-time
python main.py --live-analysis

# Không tạo biểu đồ
python main.py --no-plots
```

### 4. Sử dụng Jupyter Notebook
```bash
jupyter notebook demo.ipynb
```

## Tính năng chính

### 📊 Thu thập dữ liệu
- Hỗ trợ nhiều exchange (Binance, Coinbase, Kraken, etc.)
- Đa khung thời gian (1h, 4h, 1d)
- Validation dữ liệu tự động
- Rate limiting để tránh bị ban

### 🔧 Chỉ báo kỹ thuật
- **MACD**: Fast(12), Slow(26), Signal(9)
- **RSI**: Period(14), Oversold(30), Overbought(70)
- **Moving Averages**: EMA 20, 50, 200
- **ATR**: Cho tính toán stop loss
- **Volume indicators**: OBV, Volume SMA

### 🎯 Chiến lược Multi-Timeframe
- **HTF (Daily)**: Xác định xu hướng tổng thể
- **ITF (4H)**: Tìm setup entry/exit
- **LTF (1H)**: Timing entry chính xác
- **Confidence scoring**: 0-100% độ tin cậy

### 💰 Risk Management
- Position sizing dựa trên % risk
- Stop loss tự động (ATR-based)
- Take profit theo risk/reward ratio
- Max drawdown protection

### 📈 Backtest Engine
- Historical simulation
- Commission & slippage
- Detailed performance metrics
- Trade-by-trade analysis

### 📊 Visualization
- Interactive price charts (Plotly)
- MACD & RSI subplots
- Entry/exit signals
- Equity curve
- Drawdown analysis
- Trade distribution

## Kết quả mẫu

Dựa trên backtest 365 ngày với $10,000:

| Metric | Giá trị |
|--------|---------|
| **Total Return** | 45.2% |
| **Win Rate** | 68.5% |
| **Profit Factor** | 2.1 |
| **Max Drawdown** | 12.3% |
| **Total Trades** | 47 |
| **Avg Trade** | $96.3 |

*Lưu ý: Kết quả có thể thay đổi theo thời gian và điều kiện thị trường*

## Cấu hình

Chỉnh sửa `config.py` để tùy chỉnh:

```python
# Exchange và symbol
EXCHANGE = 'binance'
SYMBOL = 'BTC/USDT'

# Timeframes
TIMEFRAMES = {
    'HTF': '1d',
    'ITF': '4h',
    'LTF': '1h'
}

# Risk management
RISK_CONFIG = {
    'max_risk_per_trade': 0.02,  # 2%
    'risk_reward_ratio': 2.0,    # 1:2
    'stop_loss_atr_multiplier': 2.0
}
```

## API Reference

### DataFetcher
```python
fetcher = DataFetcher('binance')
data = fetcher.fetch_ohlcv('BTC/USDT', '1h', 1000)
```

### TechnicalIndicators
```python
df_with_indicators = TechnicalIndicators.calculate_all_indicators(df)
```

### BacktestEngine
```python
engine = BacktestEngine(initial_capital=10000)
results = engine.run_backtest('BTC/USDT', lookback_days=365)
```

## Troubleshooting

### Lỗi thường gặp

**1. Import Error**
```bash
pip install -r requirements.txt
```

**2. Exchange Connection Error**
- Kiểm tra kết nối internet
- Thử exchange khác: `--exchange coinbase`

**3. Insufficient Data**
- Giảm `lookback_days`
- Kiểm tra symbol có tồn tại

**4. Memory Error**
- Giảm số ngày backtest
- Sử dụng timeframe cao hơn

## Đóng góp

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push và tạo Pull Request

## License

MIT License - xem file LICENSE

## Disclaimer

⚠️ **CẢNH BÁO**: Đây là công cụ nghiên cứu và giáo dục. Không phải lời khuyên đầu tư. Giao dịch cryptocurrency có rủi ro cao. Luôn DYOR (Do Your Own Research) trước khi đầu tư.
