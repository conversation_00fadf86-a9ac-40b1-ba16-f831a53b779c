"""
Real-time Bitcoin Trading Signals - In ra console
"""

import time
import pandas as pd
from datetime import datetime, timedelta
import ccxt
from basic_strategy import BasicMacdStrategy
from indicators import TechnicalIndicators
import config

class RealTimeSignals:
    """Class để theo dõi tín hiệu real-time"""
    
    def __init__(self):
        self.exchange = ccxt.binance({'enableRateLimit': True})
        self.strategy = BasicMacdStrategy(10000)  # Virtual portfolio
        self.last_signal_time = None
        self.last_price = 0
        self.signal_count = 0
        
    def fetch_current_data(self):
        """Lấy dữ liệu hiện tại"""
        try:
            print("📊 Đang lấy dữ liệu từ Binance...")
            
            # Lấy dữ liệu 4H cho signals (200 nến)
            data_4h = self.exchange.fetch_ohlcv(config.SYMBOL, '4h', limit=200)
            
            # Lấy dữ liệu 1H cho execution (200 nến)
            data_1h = self.exchange.fetch_ohlcv(config.SYMBOL, '1h', limit=200)
            
            # Convert to DataFrame
            df_4h = pd.DataFrame(data_4h, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df_1h = pd.DataFrame(data_1h, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # Set timestamp as index
            for df in [df_4h, df_1h]:
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.set_index('timestamp', inplace=True)
                df[['open', 'high', 'low', 'close', 'volume']] = df[['open', 'high', 'low', 'close', 'volume']].astype(float)
            
            # Tính toán indicators
            df_4h_indicators = TechnicalIndicators.calculate_all_indicators(df_4h)
            df_1h_indicators = TechnicalIndicators.calculate_all_indicators(df_1h)
            
            return {
                'ITF': df_4h_indicators,
                'LTF': df_1h_indicators
            }
            
        except Exception as e:
            print(f"❌ Lỗi lấy dữ liệu: {e}")
            return None
    
    def check_signals(self, data):
        """Kiểm tra tín hiệu giao dịch"""
        try:
            # Reset strategy để check signal mới
            self.strategy.open_trade = None
            
            # Kiểm tra entry signal
            signal = self.strategy.check_entry_signals(data)
            
            if signal:
                current_time = datetime.now()
                
                # Tránh spam signals (chỉ báo 1 signal/15 phút)
                if (self.last_signal_time and 
                    current_time - self.last_signal_time < timedelta(minutes=15)):
                    return None
                
                self.last_signal_time = current_time
                return signal
            
            return None
            
        except Exception as e:
            print(f"❌ Lỗi check signals: {e}")
            return None
    
    def print_signal(self, signal: str, data: dict):
        """In tín hiệu ra console"""
        try:
            self.signal_count += 1
            current_price = data['LTF']['close'].iloc[-1]
            
            # Lấy thông tin indicators
            itf_latest = data['ITF'].iloc[-1]
            
            macd_4h = itf_latest.get('macd', 0)
            macd_signal_4h = itf_latest.get('macd_signal', 0)
            rsi_4h = itf_latest.get('rsi', 50)
            
            # Tính toán stop loss và take profit
            atr = data['LTF'].iloc[-1].get('atr', current_price * 0.02)
            
            if signal == 'long':
                emoji = "🟢"
                direction = "LONG"
                stop_loss = current_price - (atr * 2)
                take_profit = current_price + (atr * 4)
            else:
                emoji = "🔴"
                direction = "SHORT"
                stop_loss = current_price + (atr * 2)
                take_profit = current_price - (atr * 4)
            
            # Tính risk/reward
            risk = abs(current_price - stop_loss)
            reward = abs(take_profit - current_price)
            rr_ratio = reward / risk if risk > 0 else 0
            
            # In tín hiệu
            print("\n" + "="*60)
            print(f"{emoji} BITCOIN TRADING SIGNAL #{self.signal_count} {emoji}")
            print("="*60)
            print(f"🎯 Direction: {direction}")
            print(f"💰 Entry Price: ${current_price:,.2f}")
            print(f"🛑 Stop Loss: ${stop_loss:,.2f}")
            print(f"🎯 Take Profit: ${take_profit:,.2f}")
            print(f"📊 Risk/Reward: 1:{rr_ratio:.1f}")
            print(f"")
            print(f"📈 Technical Analysis:")
            print(f"   • MACD (4H): {macd_4h:.2f}")
            print(f"   • Signal (4H): {macd_signal_4h:.2f}")
            print(f"   • RSI (4H): {rsi_4h:.1f}")
            print(f"")
            print(f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"🤖 Strategy: MACD Crossover")
            print("="*60)
            
            # Lưu log
            self.log_signal(signal, current_price, stop_loss, take_profit)
            
        except Exception as e:
            print(f"❌ Lỗi print signal: {e}")
    
    def log_signal(self, signal: str, price: float, sl: float, tp: float):
        """Lưu signal vào file log"""
        try:
            log_entry = f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')},{signal},{price:.2f},{sl:.2f},{tp:.2f}\n"
            
            with open('trading_signals.log', 'a') as f:
                f.write(log_entry)
                
        except Exception as e:
            print(f"⚠️ Không thể lưu log: {e}")
    
    def print_market_status(self, data: dict):
        """In trạng thái thị trường"""
        try:
            current_price = data['LTF']['close'].iloc[-1]
            price_change = ((current_price - self.last_price) / self.last_price * 100) if self.last_price else 0
            
            # Lấy indicators
            itf_latest = data['ITF'].iloc[-1]
            
            macd_4h = itf_latest.get('macd', 0)
            macd_signal_4h = itf_latest.get('macd_signal', 0)
            rsi_4h = itf_latest.get('rsi', 50)
            
            # Trend analysis
            if macd_4h > macd_signal_4h:
                trend = "🟢 Bullish"
            else:
                trend = "🔴 Bearish"
            
            # RSI status
            if rsi_4h > 70:
                rsi_status = "🔴 Overbought"
            elif rsi_4h < 30:
                rsi_status = "🟢 Oversold"
            else:
                rsi_status = "🟡 Neutral"
            
            change_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"
            
            print(f"\n📊 Market Status - {datetime.now().strftime('%H:%M:%S')}")
            print(f"💰 BTC: ${current_price:,.2f} {change_emoji} {price_change:+.2f}%")
            print(f"📈 Trend: {trend} | RSI: {rsi_4h:.1f} ({rsi_status})")
            print(f"🔍 Waiting for signals... (Total: {self.signal_count})")
            
            self.last_price = current_price
            
        except Exception as e:
            print(f"❌ Lỗi print market status: {e}")
    
    def run(self, check_interval=300):
        """Chạy real-time monitoring"""
        print("🚀 BITCOIN REAL-TIME TRADING SIGNALS")
        print("=" * 50)
        print(f"📊 Symbol: {config.SYMBOL}")
        print(f"⏰ Check interval: {check_interval//60} minutes")
        print(f"🤖 Strategy: MACD Crossover")
        print(f"📝 Signals sẽ được lưu vào: trading_signals.log")
        print("=" * 50)
        print("🔍 Bắt đầu monitoring... (Ctrl+C để dừng)")
        
        # Tạo header cho log file
        try:
            with open('trading_signals.log', 'w') as f:
                f.write("timestamp,signal,price,stop_loss,take_profit\n")
        except:
            pass
        
        market_update_counter = 0
        
        while True:
            try:
                # Lấy dữ liệu thị trường
                data = self.fetch_current_data()
                if not data:
                    print("⚠️ Không thể lấy dữ liệu, thử lại sau 1 phút...")
                    time.sleep(60)
                    continue
                
                # Kiểm tra tín hiệu
                signal = self.check_signals(data)
                
                if signal:
                    print(f"\n🎯 PHÁT HIỆN TÍN HIỆU: {signal.upper()}!")
                    self.print_signal(signal, data)
                
                # In market status mỗi 3 lần check (15 phút)
                market_update_counter += 1
                if market_update_counter >= 3:
                    self.print_market_status(data)
                    market_update_counter = 0
                
                print(f"✅ Check completed. Next check in {check_interval//60} minutes...")
                time.sleep(check_interval)
                
            except KeyboardInterrupt:
                print("\n🛑 Monitoring stopped by user!")
                break
            except Exception as e:
                print(f"❌ Unexpected error: {e}")
                print("⏳ Waiting 60 seconds before retry...")
                time.sleep(60)

def main():
    """Main function"""
    try:
        monitor = RealTimeSignals()
        monitor.run(check_interval=300)  # Check mỗi 5 phút
        
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error starting monitor: {e}")

if __name__ == "__main__":
    main()
