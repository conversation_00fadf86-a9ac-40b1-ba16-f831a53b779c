version: '3.8'

services:
  bitcoin-trading-bot:
    build: .
    container_name: btc-trading-signals
    restart: unless-stopped
    
    # Environment variables (có thể override từ .env file)
    environment:
      - PYTHONUNBUFFERED=1
      - TZ=Asia/Ho_Chi_Minh
    
    # Mount volumes để lưu logs và data
    volumes:
      - ./logs:/app/logs
      - ./trading_signals.log:/app/trading_signals.log
      - ./results:/app/results
    
    # Network mode
    network_mode: "host"
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    # Health check
    healthcheck:
      test: ["CMD", "python3", "-c", "import requests; requests.get('https://api.binance.com/api/v3/ping', timeout=10)"]
      interval: 5m
      timeout: 30s
      retries: 3
      start_period: 30s
